import { useState, useMemo, useEffect } from 'react'

import { useCustomizationsData, useStoresData } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import {
  createTableColumns,
  ItemsInStoreButtons,
  ItemsInStoreTableSkeleton,
  ItemsInStoreDialogs,
  ItemsInStoreDataTable,
  CustomizationDialog,
  BuffetConfigModal
} from './components'
import ItemsInStoreProvider, { useItemsInStore } from './context'
import { ItemsInStoreTable } from './data'
import { useItemsInStoreForTable, useUpdateItemInStoreStatus } from './hooks'

function ItemsInStoreContent() {
  const { createOpen, copyOpen, updateOpen, deleteOpen, setCopyOpen, setUpdateOpen, setDeleteOpen, setCurrentRow } =
    useItemsInStore()
  const [isCustomizationDialogOpen, setIsCustomizationDialogOpen] = useState(false)
  const [selectedMenuItem, setSelectedMenuItem] = useState<ItemsInStoreTable | null>(null)
  const [isBuffetConfigModalOpen, setIsBuffetConfigModalOpen] = useState(false)
  const [selectedBuffetMenuItem, setSelectedBuffetMenuItem] = useState<ItemsInStoreTable | null>(null)
  const [selectedItemTypeUid, setSelectedItemTypeUid] = useState<string>('all')
  const [selectedStoreUid, setSelectedStoreUid] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('store')
  const [selectedDaysOfWeek, setSelectedDaysOfWeek] = useState<string[]>([])
  const [selectedItemStatus, setSelectedItemStatus] = useState<string>('all')

  const updateStatusMutation = useUpdateItemInStoreStatus()

  const filterParams = useMemo(() => {
    const params: Record<string, string> = {}
    if (selectedItemTypeUid !== 'all') {
      params.item_type_uid = selectedItemTypeUid
    }
    if (selectedStoreUid !== 'all') {
      params.store_uid = selectedStoreUid
    }

    if (selectedItemStatus === 'active') {
      params.active = '1'
    } else if (selectedItemStatus === 'inactive') {
      params.active = '0'
    } else if (selectedItemStatus === 'new') {
      params.apply_with_store = '2'
    } else if (selectedItemStatus === 'modified') {
      params.apply_with_store = '1'
    }

    if (selectedItemStatus === 'active' || selectedItemStatus === 'all' || selectedItemStatus === 'inactive') {
      if (selectedStatus === 'store') {
        // Get items that apply to store only
        params.apply_with_store = '-1'
      }
      // For 'store_city', don't add apply_with_store parameter (get all items)
    }

    if (selectedDaysOfWeek.length > 0) {
      params.time_sale_date_week = selectedDaysOfWeek.join(',')
    }
    return params
  }, [selectedItemTypeUid, selectedStoreUid, selectedStatus, selectedDaysOfWeek, selectedItemStatus])

  const {
    data: menuItems = [],
    isLoading: itemsLoading,
    error: itemsError
  } = useItemsInStoreForTable({
    params: filterParams
  })
  const storeUid = selectedStoreUid !== 'all' ? selectedStoreUid : undefined

  // Get all city UIDs for customizations when no specific store is selected
  const getAllCityUids = (): string[] => {
    try {
      const citiesData = localStorage.getItem('pos_cities_data')
      if (citiesData) {
        const cities: Array<{ id: string }> = JSON.parse(citiesData)
        return cities.map(city => city.id)
      }
    } catch {}
    return []
  }

  const customizationParams = storeUid ? { store_uid: storeUid } : { list_city_uid: getAllCityUids() }

  const { data: customizations = [] } = useCustomizationsData({
    skip_limit: true,
    ...customizationParams
  })
  const { data: storesData = [] } = useStoresData()

  const isLoading = itemsLoading
  const error = itemsError

  useEffect(() => {
    if (storesData.length > 0 && selectedStoreUid === 'all') {
      const firstActiveStore = storesData.find(store => store.isActive)
      if (firstActiveStore) {
        setSelectedStoreUid(firstActiveStore.id)
      }
    }
  }, [storesData, selectedStoreUid])

  const handleCustomizationClick = (menuItem: ItemsInStoreTable) => {
    setSelectedMenuItem(menuItem)
    setIsCustomizationDialogOpen(true)
  }

  const handleCopyClick = (menuItem: ItemsInStoreTable) => {
    setCurrentRow(menuItem as any) // Convert to ItemsInStore for context
    setCopyOpen(true)
  }

  const handleDeleteClick = (menuItem: ItemsInStoreTable) => {
    setCurrentRow(menuItem as any) // Convert to ItemsInStore for context
    setDeleteOpen(true)
  }

  const handleRowClick = (menuItem: ItemsInStoreTable) => {
    setCurrentRow(menuItem as any) // Convert to ItemsInStore for context
    setUpdateOpen(true)
  }

  const handleBuffetConfigClick = (menuItem: ItemsInStoreTable) => {
    setSelectedBuffetMenuItem(menuItem)
    setIsBuffetConfigModalOpen(true)
  }

  const columns = useMemo(
    () =>
      createTableColumns({
        onBuffetConfigClick: handleBuffetConfigClick,
        onCustomizationClick: handleCustomizationClick
      }),
    [handleBuffetConfigClick, handleCustomizationClick]
  )

  const handleToggleStatus = async (menuItem: ItemsInStoreTable) => {
    try {
      setSelectedItemStatus('all')

      const newStatus = menuItem.isActive ? false : true
      await updateStatusMutation.mutateAsync({
        item_uid: menuItem.id,
        active: newStatus
      })
    } catch (_error) {
      // Error handled by mutation
    }
  }

  if (error) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <p className='text-muted-foreground mb-2 text-sm'>Có lỗi xảy ra khi tải dữ liệu</p>
          <p className='text-muted-foreground text-xs'>
            {itemsError && `Món ăn: ${itemsError?.message || 'Lỗi không xác định'}`}
          </p>
          <button
            onClick={() => window.location.reload()}
            className='mt-4 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600'
          >
            Tải lại trang
          </button>
        </div>
      </div>
    )
  }

  // Check if any modal is open (similar to items-in-city pattern)
  const isAnyModalOpen = createOpen || copyOpen || updateOpen || deleteOpen

  return (
    <>
      {!isAnyModalOpen && (
        <>
          <Header>
            <div className='ml-auto flex items-center space-x-4'>
              <Search />
              <ThemeSwitch />
              <ProfileDropdown />
            </div>
          </Header>

          <Main>
            <div className='mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4'>
              <div>
                <h2 className='text-2xl font-bold tracking-tight'>Món ăn tại cửa hàng</h2>
              </div>
              <ItemsInStoreButtons />
            </div>
            <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
              {isLoading && <ItemsInStoreTableSkeleton />}
              {!isLoading && (
                <ItemsInStoreDataTable
                  columns={columns as any}
                  data={menuItems}
                  onCustomizationClick={handleCustomizationClick}
                  onCopyClick={handleCopyClick}
                  onDeleteClick={handleDeleteClick}
                  onBuffetConfigClick={handleBuffetConfigClick}
                  onToggleStatus={handleToggleStatus}
                  onRowClick={handleRowClick}
                  customizations={customizations}
                  selectedItemTypeUid={selectedItemTypeUid}
                  onItemTypeChange={setSelectedItemTypeUid}
                  selectedStoreUid={selectedStoreUid}
                  onStoreChange={setSelectedStoreUid}
                  selectedStatus={selectedStatus}
                  onStatusChange={setSelectedStatus}
                  selectedDaysOfWeek={selectedDaysOfWeek}
                  onDaysOfWeekChange={setSelectedDaysOfWeek}
                  selectedItemStatus={selectedItemStatus}
                  onItemStatusChange={setSelectedItemStatus}
                />
              )}
            </div>
          </Main>
        </>
      )}

      <ItemsInStoreDialogs />

      {isCustomizationDialogOpen && selectedMenuItem && (
        <CustomizationDialog
          open={isCustomizationDialogOpen}
          onOpenChange={setIsCustomizationDialogOpen}
          menuItem={selectedMenuItem as any}
          menuItems={menuItems as any}
          customizations={customizations}
        />
      )}

      {isBuffetConfigModalOpen && selectedBuffetMenuItem && (
        <BuffetConfigModal
          open={isBuffetConfigModalOpen}
          onOpenChange={setIsBuffetConfigModalOpen}
          menuItem={selectedBuffetMenuItem as any}
          menuItems={menuItems as any}
          isDetailMode={false}
        />
      )}
    </>
  )
}

export default function ItemsInStorePage() {
  return (
    <ItemsInStoreProvider>
      <ItemsInStoreContent />
    </ItemsInStoreProvider>
  )
}
