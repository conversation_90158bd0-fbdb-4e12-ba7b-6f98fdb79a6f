/**
 * Design System - Token Exports
 * 
 * This file provides a unified interface for accessing all design tokens.
 * Import from here to ensure consistency across the application.
 */

// Color tokens
export {
  navyBlue,
  slate,
  neutral,
  emerald,
  red,
  amber,
  semanticColors,
  chartColors,
  colorPalettes,
  getContrastColor,
  type ColorToken,
  type ColorPalette,
} from './colors'

// Typography tokens
export {
  fontFamilies,
  fontWeights,
  fontSizes,
  lineHeights,
  letterSpacing,
  typographyScale,
  type FontFamily,
  type FontWeight,
  type FontSize,
  type LineHeight,
  type LetterSpacing,
  type TypographyVariant,
} from './typography'

// Spacing and layout tokens
export {
  spacing,
  borderRadius,
  shadows,
  zIndex,
  componentSpacing,
  breakpoints,
  maxWidth,
  duration,
  timingFunction,
  type Spacing,
  type BorderRadius,
  type Shadow,
  type ZIndex,
  type Breakpoint,
  type MaxWidth,
  type Duration,
  type TimingFunction,
} from './spacing'

// Import for internal use
import { semanticColors } from './colors'
import { typographyScale } from './typography'
import { 
  spacing as spacingTokens, 
  borderRadius as borderRadiusTokens, 
  shadows as shadowTokens,
  breakpoints as breakpointsTokens,
  maxWidth as maxWidthTokens,
  duration as durationTokens,
  timingFunction as timingFunctionTokens,
  zIndex as zIndexTokens
} from './spacing'

// Theme configuration
export const theme = {
  colors: semanticColors,
  spacing: spacingTokens,
  borderRadius: borderRadiusTokens,
  shadows: shadowTokens,
  typography: typographyScale,
  breakpoints: breakpointsTokens,
  maxWidth: maxWidthTokens,
  animation: {
    duration: durationTokens,
    timingFunction: timingFunctionTokens,
  },
  zIndex: zIndexTokens,
} as const

// CSS custom property generators
export const generateCSSCustomProperties = (isDark = false) => {
  const colors = semanticColors
  const mode = isDark ? 'dark' : 'light'
  
  return {
    // Color variables
    '--color-primary': colors.primary[mode],
    '--color-primary-foreground': colors.primaryForeground[mode],
    '--color-secondary': colors.secondary[mode],
    '--color-secondary-foreground': colors.secondaryForeground[mode],
    '--color-muted': colors.muted[mode],
    '--color-muted-foreground': colors.mutedForeground[mode],
    '--color-accent': colors.accent[mode],
    '--color-accent-foreground': colors.accentForeground[mode],
    '--color-background': colors.background[mode],
    '--color-foreground': colors.foreground[mode],
    '--color-card': colors.card[mode],
    '--color-card-foreground': colors.cardForeground[mode],
    '--color-popover': colors.popover[mode],
    '--color-popover-foreground': colors.popoverForeground[mode],
    '--color-border': colors.border[mode],
    '--color-input': colors.input[mode],
    '--color-ring': colors.ring[mode],
    '--color-destructive': colors.destructive[mode],
    '--color-destructive-foreground': colors.destructiveForeground[mode],
    '--color-success': colors.success[mode],
    '--color-success-foreground': colors.successForeground[mode],
    '--color-warning': colors.warning[mode],
    '--color-warning-foreground': colors.warningForeground[mode],
    
    // Sidebar variables
    '--color-sidebar': colors.sidebar[mode],
    '--color-sidebar-foreground': colors.sidebarForeground[mode],
    '--color-sidebar-primary': colors.sidebarPrimary[mode],
    '--color-sidebar-primary-foreground': colors.sidebarPrimaryForeground[mode],
    '--color-sidebar-accent': colors.sidebarAccent[mode],
    '--color-sidebar-accent-foreground': colors.sidebarAccentForeground[mode],
    '--color-sidebar-border': colors.sidebarBorder[mode],
    '--color-sidebar-ring': colors.sidebarRing[mode],
    
    // Spacing variables
    '--radius': borderRadiusTokens.lg,
    '--radius-sm': borderRadiusTokens.sm,
    '--radius-md': borderRadiusTokens.md,
    '--radius-lg': borderRadiusTokens.lg,
    '--radius-xl': borderRadiusTokens.xl,
  }
}

export type Theme = typeof theme 