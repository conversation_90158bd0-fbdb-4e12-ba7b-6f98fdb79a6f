import { useState, useMemo } from 'react'

import { useOrderLogsData } from '@/hooks/api'

import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, Button, Input } from '@/components/ui'

import { DateRangePicker } from './date-range-picker'

interface OrderLogModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  deviceCode?: string
}

interface OrderLogEntry {
  id: string
  orderNumber: string
  customerName: string
  orderTime: string
  status: string
  total: number
  items?: Array<{
    name: string
    quantity: number
    price: number
  }>
  subtotal?: number
  discount?: number
  finalTotal?: number
  customerCount?: number
  customerType?: string
  phoneNumber?: string
  notes?: string
}

export function OrderLogModal({ open, onOpenChange, deviceCode }: OrderLogModalProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [startDate, setStartDate] = useState<Date>(() => {
    const today = new Date()
    return new Date(today.getFullYear(), today.getMonth(), 1) // First day of current month
  })
  const [endDate, setEndDate] = useState<Date>(() => {
    const today = new Date()
    return new Date(today.getFullYear(), today.getMonth() + 1, 0) // Last day of current month
  })
  const [selectedOrder, setSelectedOrder] = useState<any>(null)

  // Validate that start and end dates are in the same month
  const datesValid = useMemo(() => {
    return startDate.getMonth() === endDate.getMonth() && startDate.getFullYear() === endDate.getFullYear()
  }, [startDate, endDate])

  // Convert dates to timestamps for API call
  const { startTimestamp, endTimestamp } = useMemo(() => {
    const start = startDate.getTime()
    const end = endDate.getTime() + 24 * 60 * 60 * 1000 - 1 // End of day
    return { startTimestamp: start, endTimestamp: end }
  }, [startDate, endDate])

  // Fetch order logs data
  const { data: orderLogsData, isLoading } = useOrderLogsData({
    device_code: deviceCode || '',
    start_date: startTimestamp,
    end_date: endTimestamp,
    search: searchTerm,
    enabled: open && !!deviceCode && datesValid
  })

  // Transform API data to display format
  const transformedLogs = useMemo(() => {
    if (!orderLogsData) return []

    return orderLogsData.map((log: any) => ({
      id: log.tran_id,
      orderNumber: `#${log.change_data.tran_no || log.tran_id.slice(-6)}`,
      customerName: log.table_name || log.change_data.table_name || 'Không xác định',
      orderTime: new Date(log.change_data.start_date || log.start_date).toLocaleString('vi-VN'),
      status: log.change_data.sale_note || 'Hoàn thành',
      total: log.change_data.total_amount || 0,
      items:
        log.change_data.sale_detail?.map((item: any) => ({
          name: item.item_name,
          quantity: item.quantity,
          price: item.amount
        })) || [],
      subtotal: log.change_data.total_amount || 0,
      discount: log.change_data.extra_data?.deposit_amount || 0,
      finalTotal: log.change_data.total_amount || 0,
      customerCount: log.change_data.extra_data?.peo_count || 0,
      customerType: log.change_data.extra_data?.customer_name || '',
      phoneNumber: log.change_data.extra_data?.customer_phone || '',
      notes: log.change_data.extra_data?.note_deposit || log.change_data.sale_note || ''
    }))
  }, [orderLogsData])

  const filteredLogs = transformedLogs.filter(log => {
    const matchesSearch = log.orderNumber.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesSearch
  })

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN').format(amount) + ' đ'
  }

  const handleOrderClick = (order: OrderLogEntry) => {
    setSelectedOrder(order)
  }

  const handleBackToList = () => {
    setSelectedOrder(null)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-4xl'>
        <DialogHeader>
          <DialogTitle>Nhật ký order</DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          {/* Search and Date Range */}
          <div className='flex gap-4'>
            <div className='flex-1'>
              <Input
                placeholder='Tìm kiếm theo mã hóa đơn'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
              />
            </div>
            <div className='w-64'>
              <DateRangePicker
                startDate={startDate}
                endDate={endDate}
                onStartDateChange={date => date && setStartDate(date)}
                onEndDateChange={date => date && setEndDate(date)}
                placeholder='Chọn khoảng thời gian'
              />
            </div>
          </div>

          {/* Date validation warning */}
          {!datesValid && (
            <div className='rounded bg-red-50 p-2 text-sm text-red-600'>
              ⚠️ Ngày bắt đầu và ngày kết thúc phải cùng tháng
            </div>
          )}

          {/* Content */}
          {selectedOrder ? (
            // Order Detail View
            <div className='space-y-4'>
              <div className='rounded-lg border bg-gray-50 p-4'>
                <div className='mb-4 flex items-start justify-between'>
                  <div>
                    <h3 className='text-lg font-medium'>
                      {selectedOrder.orderNumber} - {selectedOrder.customerName}
                    </h3>
                    <p className='text-sm text-gray-600'>{selectedOrder.status}</p>
                  </div>
                  <div className='text-right'>
                    <p className='text-sm text-gray-600'>{selectedOrder.orderTime}</p>
                    <p className='text-lg font-medium'>{formatCurrency(selectedOrder.total)}</p>
                  </div>
                </div>

                {/* Order Items */}
                {selectedOrder.items && (
                  <div className='mb-4 space-y-2'>
                    {selectedOrder.items.map((item: any, index: number) => (
                      <div key={index} className='flex justify-between'>
                        <span>
                          (x{item.quantity}) {item.name}
                        </span>
                        <span>{formatCurrency(item.price)}</span>
                      </div>
                    ))}
                  </div>
                )}

                {/* Order Summary */}
                <div className='space-y-2 border-t pt-4'>
                  <div className='flex justify-between'>
                    <span>Thành tiền</span>
                    <span>{formatCurrency(selectedOrder.subtotal || selectedOrder.total)}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span>Đặt cọc</span>
                    <span>{selectedOrder.discount ? formatCurrency(selectedOrder.discount) : '0 đ'}</span>
                  </div>
                  <div className='flex justify-between border-t pt-2 text-lg font-medium'>
                    <span>Tổng tiền</span>
                    <span>{formatCurrency(selectedOrder.finalTotal || selectedOrder.total)}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span>Số khách</span>
                    <span>{selectedOrder.customerCount || 0}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span>Khách hàng</span>
                    <span>{selectedOrder.customerType || ''}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span>Số điện thoại</span>
                    <span>{selectedOrder.phoneNumber || ''}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span>Ghi chú</span>
                    <span>{selectedOrder.notes || ''}</span>
                  </div>
                </div>

                <div className='mt-4 text-center'>
                  <Button variant='link' onClick={handleBackToList} className='text-blue-600'>
                    Thu gọn
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            // Order List View
            <div className='max-h-96 overflow-y-auto'>
              {isLoading ? (
                <div className='py-8 text-center text-gray-500'>Đang tải dữ liệu...</div>
              ) : filteredLogs.length === 0 ? (
                <div className='py-8 text-center text-gray-500'>Không tìm thấy order nào</div>
              ) : (
                <div className='space-y-2'>
                  {filteredLogs.map(log => (
                    <div
                      key={log.id}
                      className='cursor-pointer rounded-lg border p-4 hover:bg-gray-50'
                      onClick={() => handleOrderClick(log)}
                    >
                      <div className='flex items-start justify-between'>
                        <div className='flex-1'>
                          <div className='mb-1 flex items-center gap-2'>
                            <span className='font-medium text-blue-600'>{log.orderNumber}</span>
                            <span className='text-sm text-gray-500'>•</span>
                            <span className='text-sm text-gray-600'>{log.customerName}</span>
                          </div>
                          <div className='text-sm text-gray-500'>{log.status}</div>
                        </div>
                        <div className='text-right'>
                          <div className='text-sm text-gray-600'>{log.orderTime}</div>
                          <div className='font-medium text-green-600'>{formatCurrency(log.total)}</div>
                        </div>
                      </div>
                      <div className='mt-2 text-center'>
                        <Button variant='link' className='text-sm text-blue-600'>
                          Xem chi tiết
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
