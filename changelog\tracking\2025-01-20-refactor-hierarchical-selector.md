# Refactor Hierarchical Selector - Simplified Architecture

## Vấn đề ban đầu
- <PERSON>hi click vào một store cụ thể (ví dụ: "A Hưng"), UI hiển thị tick ở tất cả các item khác một cách sai lệch
- <PERSON><PERSON> lượng hiển thị đúng (3 mục) nhưng UI checkbox hiển thị sai
- Logic phức tạp với việc lưu trữ brand, city và store cùng lúc gây confusion

## Giải pháp: Tổ chức lại cấu trúc đơn giản

### Nguyên tắc mới:
1. **Single Source of Truth**: Chỉ lưu trữ `selectedStores` (Set<string>) làm nguồn sự thật duy nhất
2. **Computed States**: Tính toán trạng thái city và brand dựa trên stores được chọn
3. **Clear Logic**: Logic đơn giản, d<PERSON> hiể<PERSON> và maintain

### C<PERSON><PERSON> trúc dữ liệu mới:
```typescript
// Trước: <PERSON><PERSON><PERSON> trữ hỗn hợp
[
    "brand:d43a01ec-2f38-4430-a7ca-9b3324f7d39e",
    "city:ce8ca87c-e4b9-402a-8acc-3136f6bcf42d", 
    "store:e20d55dd-6dcc-4238-a32e-42f8ae6abaeb",
    // ... phức tạp và dễ gây lỗi
]

// Sau: Chỉ lưu stores
selectedStores = Set([
    "e20d55dd-6dcc-4238-a32e-42f8ae6abaeb",
    "489d0728-24ae-4149-8560-4835dc81ede6",
    // ... đơn giản và rõ ràng
])
```

### Logic functions mới:

#### 1. Helper Functions
```typescript
// Kiểm tra city được chọn hoàn toàn (tất cả stores)
const isCityFullySelected = (cityId: string): boolean => {
  return city.stores.every(store => selectedStores.has(store.id))
}

// Kiểm tra city được chọn một phần (một số stores)
const isCityPartiallySelected = (cityId: string): boolean => {
  const selectedCount = city.stores.filter(store => selectedStores.has(store.id)).length
  return selectedCount > 0 && selectedCount < city.stores.length
}

// Tương tự cho brand
const isBrandFullySelected = (brandId: string): boolean => {
  return brand.cities.every(city => isCityFullySelected(city.id))
}

const isBrandPartiallySelected = (brandId: string): boolean => {
  if (isBrandFullySelected(brandId)) return false // Không indeterminate nếu đã fully selected
  return brand.cities.some(city => isCityFullySelected(city.id) || isCityPartiallySelected(city.id))
}
```

#### 2. Interface Functions (cho UI)
```typescript
const isItemSelected = (itemId: string, type: 'brand' | 'city' | 'store'): boolean => {
  if (type === 'store') return selectedStores.has(itemId)
  if (type === 'city') return isCityFullySelected(itemId)
  if (type === 'brand') return isBrandFullySelected(itemId)
  return false
}

const isItemIndeterminate = (itemId: string, type: 'brand' | 'city'): boolean => {
  if (type === 'city') return isCityPartiallySelected(itemId)
  if (type === 'brand') return isBrandPartiallySelected(itemId)
  return false
}
```

#### 3. Toggle Logic
```typescript
const handleItemToggle = (itemId: string, type: 'brand' | 'city' | 'store') => {
  const newSelectedStores = new Set(selectedStores)

  if (type === 'brand') {
    if (isBrandFullySelected(itemId)) {
      // Unselect: remove all stores in brand
      brand.cities.forEach(city => {
        city.stores.forEach(store => newSelectedStores.delete(store.id))
      })
    } else {
      // Select: add all stores in brand
      brand.cities.forEach(city => {
        city.stores.forEach(store => newSelectedStores.add(store.id))
      })
    }
  }
  // Tương tự cho city và store...
  
  setSelectedStores(newSelectedStores)
}
```

## Lợi ích của cấu trúc mới:

### ✅ Đơn giản hóa
- Chỉ một nguồn dữ liệu: `selectedStores`
- Logic rõ ràng, dễ hiểu
- Ít bug hơn do ít complexity

### ✅ Performance
- Không cần consolidation phức tạp
- Tính toán on-demand
- Ít state updates

### ✅ Maintainability  
- Code dễ đọc và debug
- Logic tách biệt rõ ràng
- Dễ test và verify

### ✅ Correctness
- Không còn hiển thị tick sai
- Indeterminate state chính xác
- Count chính xác (selectedStores.size)

## Kết quả test:

### Test Case 1: Click "A Hưng"
- ✅ Chỉ "A Hưng" được tick
- ✅ City "Hà Nội" hiển thị indeterminate
- ✅ Brand hiển thị indeterminate
- ✅ Các item khác không bị ảnh hưởng

### Test Case 2: Complete một city
- ✅ City hiển thị fully selected
- ✅ Brand vẫn indeterminate (chưa complete)

### Test Case 3: Complete toàn bộ brand
- ✅ Brand hiển thị fully selected
- ✅ Không còn indeterminate state

## Files thay đổi:
- `src/features/employee/detail/hooks/use-brand-city-store-selector.ts`
- `src/features/employee/detail/components/selectors/brand-city-store-selector.tsx`
- `src/features/employee/detail/hooks/use-selected-items-summary.ts`
- `src/features/employee/detail/components/summary/selected-items-summary.tsx`

## UI Count Fix:

### Vấn đề UI Count:
- Button "Lưu" hiển thị số lượng sai (đếm theo summary entries thay vì stores)
- Summary header hiển thị số lượng không khớp với button
- Ví dụ: Chọn 2 stores trong cùng city → Button hiển thị "1 mục" thay vì "2 mục"

### Giải pháp:
1. **Button Count**: Sử dụng `getSelectedCount()` thay vì `localSelectedItems.size`
2. **Summary Count**: Thêm `selectedStoreCount` trong `useSelectedItemsSummary`
3. **Logic Update**: Cập nhật `selectedSummary` để tương thích với cấu trúc mới
4. **Conditional Display**: Hiển thị store count cho "selected" mode, summary count cho "current" mode

### Kết quả:
- ✅ Button "Lưu (X mục)" hiển thị đúng số stores được chọn
- ✅ Summary header "(X mục)" khớp với button
- ✅ Summary content vẫn hiển thị consolidated view (brands/cities khi fully selected)
- ✅ Logic tương thích với cả new và old data format

## Commit message:
```
refactor(employee): fix hierarchical selector UI count and simplify architecture

- Replace complex mixed selection state with simple selectedStores Set
- Compute city/brand states from store selections on-demand
- Eliminate consolidation logic that caused UI display bugs
- Add clear helper functions for fully/partially selected states
- Fix indeterminate logic to prevent conflicts with selected state
- Fix UI count display to show actual store count instead of summary entries
- Update summary logic to work with new store-only selection format
- Add selectedStoreCount for accurate count display in summary components
- Improve performance by reducing state complexity
- Resolves checkbox display issues and count mismatches when selecting stores
```
