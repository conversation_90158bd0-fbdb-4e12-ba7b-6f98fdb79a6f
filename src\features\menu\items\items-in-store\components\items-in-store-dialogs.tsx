import { ConfirmDialog } from '@/components/confirm-dialog'

import { useItemsInStore } from '../context'
import { ItemsInStoreMutate } from './items-in-store-mutate'

export function ItemsInStoreDialogs() {
  const {
    createOpen,
    setCreateOpen,
    copyOpen,
    setCopyOpen,
    updateOpen,
    setUpdateOpen,
    deleteOpen,
    setDeleteOpen,
    currentRow,
    setCurrentRow
  } = useItemsInStore()

  return (
    <>
      <ItemsInStoreMutate
        key='items-in-store-create'
        open={createOpen}
        onOpenChange={isOpen => {
          setCreateOpen(isOpen)
        }}
      />

      {currentRow && (
        <>
          <ItemsInStoreMutate
            key={`items-in-store-copy-${currentRow.id}`}
            open={copyOpen}
            onOpenChange={isOpen => {
              setCopyOpen(isOpen)
              if (!isOpen) {
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            currentRow={currentRow}
            isCopyMode={true}
          />

          <ItemsInStoreMutate
            key={`items-in-store-update-${currentRow.id}`}
            open={updateOpen}
            onOpenChange={isOpen => {
              setUpdateOpen(isOpen)
              if (!isOpen) {
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            currentRow={currentRow}
          />

          <ConfirmDialog
            key='quantity-day-delete'
            destructive
            open={deleteOpen}
            onOpenChange={isOpen => {
              if (!isOpen) {
                setDeleteOpen(false)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            handleConfirm={async () => {
              setDeleteOpen(false)
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
              // TODO: Add delete mutation when available
            }}
            className='max-w-md'
            title={`Bạn có muốn xoá ?`}
            desc={<>Hành động không thể hoàn tác.</>}
            confirmText='Xoá'
          />
        </>
      )}
    </>
  )
}
