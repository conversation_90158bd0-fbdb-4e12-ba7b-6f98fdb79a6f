import type { User } from '@/types'
import { useSelectedItemsSummary } from '../../hooks'
import type { BrandWithCities, EmployeeData, UserData } from '../../types'

interface SelectedItemsSummaryProps {
  hierarchicalData?: BrandWithCities[]
  localSelectedItems?: Set<string>
  employeeData?: EmployeeData
  userData?: User | UserData
}

export function SelectedItemsSummary({
  hierarchicalData,
  localSelectedItems,
  employeeData,
  userData,
}: SelectedItemsSummaryProps) {
  const { summary, summaryType, isEmpty } = useSelectedItemsSummary({
    hierarchicalData,
    localSelectedItems,
    employeeData,
    userData,
  })

  return (
    <div className='space-y-3'>
      <div className='rounded-md border bg-blue-50 p-3'>
        <div className='text-center text-sm font-medium text-blue-700'>
          {summaryType === 'current' ? '<PERSON>uy<PERSON><PERSON> truy cập hiện tại' : 'Đ<PERSON> chọn'} (
          {summary.length} mục)
        </div>
      </div>

      {!isEmpty ? (
        <div className='rounded-md border bg-gray-50'>
          <div className='grid grid-cols-2 border-b bg-gray-100'>
            <div className='p-3 text-sm font-semibold text-gray-800'>
              Thương hiệu
            </div>
            <div className='p-3 text-sm font-semibold text-gray-800'>
              Quyền truy cập
            </div>
          </div>
          {summary.map((item, index) => (
            <div
              key={index}
              className='grid grid-cols-2 border-b bg-white last:border-b-0 hover:bg-gray-50'
            >
              <div className='p-3 text-sm font-medium text-gray-700'>
                {item.brandName}
              </div>
              <div className='p-3 text-sm text-gray-600'>
                {item.accessLevel}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className='rounded-md border border-dashed border-gray-300 bg-gray-50 p-6'>
          <div className='text-center text-sm text-gray-500'>
            {summaryType === 'current'
              ? 'Nhân viên chưa có quyền truy cập thương hiệu, thành phố hoặc cửa hàng nào'
              : 'Chưa chọn thương hiệu, thành phố hoặc cửa hàng nào'}
          </div>
        </div>
      )}

      {summaryType === 'selected' && (
        <div className='rounded-md border bg-blue-50 p-3'>
          <div className='text-center text-sm text-blue-600'>
            Chọn thương hiệu và thành phố, nhà hàng từ danh sách bên dưới
          </div>
        </div>
      )}
    </div>
  )
}
