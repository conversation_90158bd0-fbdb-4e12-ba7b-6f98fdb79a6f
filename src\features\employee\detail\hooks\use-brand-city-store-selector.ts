import { useState, useMemo } from 'react'

import { useHierarchicalData } from './use-hierarchical-data'

export function useBrandCityStoreSelector(selectedItems: string[]) {
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedBrands, setExpandedBrands] = useState<Set<string>>(new Set())
  const [expandedCities, setExpandedCities] = useState<Set<string>>(new Set())
  const [localSelectedItems, setLocalSelectedItems] = useState<Set<string>>(new Set(selectedItems))

  const { hierarchicalData, filterHierarchicalData } = useHierarchicalData()

  const filteredData = useMemo(() => {
    return filterHierarchicalData(searchTerm)
  }, [filterHierarchicalData, searchTerm])

  const toggleBrandExpansion = (brandId: string) => {
    const newExpanded = new Set(expandedBrands)
    if (newExpanded.has(brandId)) {
      newExpanded.delete(brandId)
    } else {
      newExpanded.add(brandId)
    }
    setExpandedBrands(newExpanded)
  }

  const toggleCityExpansion = (cityId: string) => {
    const newExpanded = new Set(expandedCities)
    if (newExpanded.has(cityId)) {
      newExpanded.delete(cityId)
    } else {
      newExpanded.add(cityId)
    }
    setExpandedCities(newExpanded)
  }

  const handleItemToggle = (itemId: string, type: 'brand' | 'city' | 'store') => {
    console.log('🔄 Toggle:', { itemId, type })
    const newSelected = new Set(localSelectedItems)

    if (type === 'brand') {
      const brand = hierarchicalData.find(b => b.id === itemId)
      if (!brand) return

      if (isItemSelected(itemId, 'brand')) {
        // Unselect brand - remove everything
        console.log('❌ Removing brand and all children')
        newSelected.delete(`brand:${itemId}`)
        brand.cities.forEach(city => {
          newSelected.delete(`city:${city.id}`)
          city.stores.forEach(store => {
            newSelected.delete(`store:${store.id}`)
          })
        })
      } else {
        // Select brand - add all individual items (no consolidation)
        console.log('✅ Adding brand - selecting all children individually')
        brand.cities.forEach(city => {
          city.stores.forEach(store => {
            newSelected.add(`store:${store.id}`)
          })
        })
        // Then consolidate upwards
        brand.cities.forEach(city => {
          updateCityStatus(newSelected, city)
        })
        updateBrandStatus(newSelected, brand)
      }
    } else if (type === 'city') {
      const city = hierarchicalData.flatMap(b => b.cities).find(c => c.id === itemId)
      if (!city) return

      if (isItemSelected(itemId, 'city')) {
        // Unselect city - remove city and all stores
        console.log('❌ Removing city and all stores')
        newSelected.delete(`city:${itemId}`)
        city.stores.forEach(store => {
          newSelected.delete(`store:${store.id}`)
        })
      } else {
        // Select city - add all stores individually
        console.log('✅ Adding city - selecting all stores individually')
        city.stores.forEach(store => {
          newSelected.add(`store:${store.id}`)
        })
        // Then consolidate upwards
        updateCityStatus(newSelected, city)
      }

      // Update brand status
      const brand = hierarchicalData.find(b => b.cities.some(c => c.id === itemId))
      if (brand) {
        updateBrandStatus(newSelected, brand)
      }
    } else if (type === 'store') {
      const wasSelected = newSelected.has(`store:${itemId}`)

      if (wasSelected) {
        console.log('❌ Removing store')
        newSelected.delete(`store:${itemId}`)
      } else {
        console.log('✅ Adding store')
        newSelected.add(`store:${itemId}`)
      }

      // Find parents and update their status
      const city = hierarchicalData.flatMap(b => b.cities).find(c => c.stores.some(s => s.id === itemId))
      const brand = hierarchicalData.find(b => b.cities.some(c => c.stores.some(s => s.id === itemId)))

      if (city && brand) {
        updateCityStatus(newSelected, city)
        updateBrandStatus(newSelected, brand)
      }
    }

    console.log('🔚 Final selection:', Array.from(newSelected))
    setLocalSelectedItems(newSelected)
  }

  // Simple helper: Update city based on its stores
  const updateCityStatus = (selection: Set<string>, city: any) => {
    const selectedStores = city.stores.filter((s: any) => selection.has(`store:${s.id}`))

    if (selectedStores.length === 0) {
      // No stores selected - remove city
      selection.delete(`city:${city.id}`)
    } else if (selectedStores.length === city.stores.length) {
      // All stores selected - add city for consolidation
      selection.add(`city:${city.id}`)
    } else {
      // Some stores selected - remove city (show as indeterminate)
      selection.delete(`city:${city.id}`)
    }
  }

  // Simple helper: Update brand based on its cities
  const updateBrandStatus = (selection: Set<string>, brand: any) => {
    const fullySelectedCities = brand.cities.filter((c: any) => {
      return selection.has(`city:${c.id}`) || c.stores.every((s: any) => selection.has(`store:${s.id}`))
    })

    if (fullySelectedCities.length === 0) {
      // No cities fully selected - remove brand
      selection.delete(`brand:${brand.id}`)
    } else if (fullySelectedCities.length === brand.cities.length) {
      // All cities fully selected - add brand for consolidation
      selection.add(`brand:${brand.id}`)
    } else {
      // Some cities selected - remove brand (show as indeterminate)
      selection.delete(`brand:${brand.id}`)
    }
  }

  const isItemSelected = (itemId: string, type: 'brand' | 'city' | 'store'): boolean => {
    // Direct selection
    if (localSelectedItems.has(`${type}:${itemId}`)) return true

    // Check parent selections
    if (type === 'city') {
      const brand = hierarchicalData.find(b => b.cities.some(c => c.id === itemId))
      return Boolean(brand && localSelectedItems.has(`brand:${brand.id}`))
    } else if (type === 'store') {
      const city = hierarchicalData.flatMap(b => b.cities).find(c => c.stores.some(s => s.id === itemId))
      if (city && localSelectedItems.has(`city:${city.id}`)) return true

      const brand = hierarchicalData.find(b => b.cities.some(c => c.stores.some(s => s.id === itemId)))
      return Boolean(brand && localSelectedItems.has(`brand:${brand.id}`))
    }

    return false
  }

  const isItemIndeterminate = (itemId: string, type: 'brand' | 'city') => {
    if (localSelectedItems.has(`${type}:${itemId}`)) return false

    if (type === 'brand') {
      const brand = hierarchicalData.find(b => b.id === itemId)
      if (!brand) return false

      return brand.cities.some(
        (city: any) =>
          localSelectedItems.has(`city:${city.id}`) ||
          city.stores.some((store: any) => localSelectedItems.has(`store:${store.id}`))
      )
    } else if (type === 'city') {
      const city = hierarchicalData.flatMap(b => b.cities).find(c => c.id === itemId)
      if (!city) return false

      // If parent brand is selected, city is not indeterminate
      const brand = hierarchicalData.find(b => b.cities.some(c => c.id === itemId))
      if (brand && localSelectedItems.has(`brand:${brand.id}`)) return false

      return city.stores.some((store: any) => localSelectedItems.has(`store:${store.id}`))
    }

    return false
  }

  // Count actual selected items (stores + individual cities + individual brands)
  const getSelectedCount = () => {
    let count = 0

    hierarchicalData.forEach(brand => {
      brand.cities.forEach(city => {
        city.stores.forEach(store => {
          if (isItemSelected(store.id, 'store')) {
            count++
          }
        })
      })
    })

    return count
  }

  return {
    searchTerm,
    setSearchTerm,
    expandedBrands,
    expandedCities,
    localSelectedItems,
    setLocalSelectedItems,
    hierarchicalData,
    filteredData,
    toggleBrandExpansion,
    toggleCityExpansion,
    handleItemToggle,
    isItemSelected,
    isItemIndeterminate,
    getSelectedCount
  }
}
