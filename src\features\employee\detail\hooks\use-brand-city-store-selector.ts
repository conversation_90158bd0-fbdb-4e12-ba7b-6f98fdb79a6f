import { useState, useMemo } from 'react'

import { useHierarchicalData } from './use-hierarchical-data'

export function useBrandCityStoreSelector(selectedItems: string[]) {
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedBrands, setExpandedBrands] = useState<Set<string>>(new Set())
  const [expandedCities, setExpandedCities] = useState<Set<string>>(new Set())
  const [localSelectedItems, setLocalSelectedItems] = useState<Set<string>>(new Set(selectedItems))

  const { hierarchicalData, filterHierarchicalData } = useHierarchicalData()

  const filteredData = useMemo(() => {
    return filterHierarchicalData(searchTerm)
  }, [filterHierarchicalData, searchTerm])

  const toggleBrandExpansion = (brandId: string) => {
    const newExpanded = new Set(expandedBrands)
    if (newExpanded.has(brandId)) {
      newExpanded.delete(brandId)
    } else {
      newExpanded.add(brandId)
    }
    setExpandedBrands(newExpanded)
  }

  const toggleCityExpansion = (cityId: string) => {
    const newExpanded = new Set(expandedCities)
    if (newExpanded.has(cityId)) {
      newExpanded.delete(cityId)
    } else {
      newExpanded.add(cityId)
    }
    setExpandedCities(newExpanded)
  }

  const handleItemToggle = (itemId: string, type: 'brand' | 'city' | 'store') => {
    console.log('🔄 Toggle:', { itemId, type })
    const newSelected = new Set(localSelectedItems)

    if (type === 'brand') {
      const brand = hierarchicalData.find(b => b.id === itemId)
      if (!brand) return

      const currentlySelected = isItemSelected(itemId, 'brand')

      if (currentlySelected) {
        // Unselect brand - remove brand and all children
        console.log('❌ Removing brand and all children')
        newSelected.delete(`brand:${itemId}`)
        brand.cities.forEach(city => {
          newSelected.delete(`city:${city.id}`)
          city.stores.forEach(store => {
            newSelected.delete(`store:${store.id}`)
          })
        })
      } else {
        // Select brand - add all stores individually, then consolidate
        console.log('✅ Adding brand - selecting all children')
        brand.cities.forEach(city => {
          city.stores.forEach(store => {
            newSelected.add(`store:${store.id}`)
          })
        })
        // Consolidate upwards
        brand.cities.forEach(city => {
          updateCityStatus(newSelected, city)
        })
        updateBrandStatus(newSelected, brand)
      }
    } else if (type === 'city') {
      const city = hierarchicalData.flatMap(b => b.cities).find(c => c.id === itemId)
      if (!city) return

      const currentlySelected = isItemSelected(itemId, 'city')

      if (currentlySelected) {
        // Unselect city - remove city and all stores
        console.log('❌ Removing city and all stores')
        newSelected.delete(`city:${itemId}`)
        city.stores.forEach(store => {
          newSelected.delete(`store:${store.id}`)
        })
      } else {
        // Select city - add all stores individually, then consolidate
        console.log('✅ Adding city - selecting all stores')
        city.stores.forEach(store => {
          newSelected.add(`store:${store.id}`)
        })
        updateCityStatus(newSelected, city)
      }

      // Update parent brand status
      const brand = hierarchicalData.find(b => b.cities.some(c => c.id === itemId))
      if (brand) {
        updateBrandStatus(newSelected, brand)
      }
    } else if (type === 'store') {
      const currentlySelected = newSelected.has(`store:${itemId}`)

      if (currentlySelected) {
        console.log('❌ Removing store')
        newSelected.delete(`store:${itemId}`)
      } else {
        console.log('✅ Adding store')
        newSelected.add(`store:${itemId}`)
      }

      // Update parent statuses
      const city = hierarchicalData.flatMap(b => b.cities).find(c => c.stores.some(s => s.id === itemId))
      const brand = hierarchicalData.find(b => b.cities.some(c => c.stores.some(s => s.id === itemId)))

      if (city) {
        updateCityStatus(newSelected, city)
      }
      if (brand) {
        updateBrandStatus(newSelected, brand)
      }
    }

    console.log('🔚 Final selection:', Array.from(newSelected))
    setLocalSelectedItems(newSelected)
  }

  // Helper: Update city consolidation status based on its stores
  const updateCityStatus = (selection: Set<string>, city: any) => {
    const selectedStores = city.stores.filter((s: any) => selection.has(`store:${s.id}`))

    if (selectedStores.length === 0) {
      // No stores selected - remove city
      selection.delete(`city:${city.id}`)
    } else if (selectedStores.length === city.stores.length && city.stores.length > 0) {
      // All stores selected - add city for consolidation (but keep stores for accurate counting)
      selection.add(`city:${city.id}`)
    } else {
      // Some stores selected - remove city (will show as indeterminate)
      selection.delete(`city:${city.id}`)
    }
  }

  // Helper: Update brand consolidation status based on its cities
  const updateBrandStatus = (selection: Set<string>, brand: any) => {
    // Check if all cities are fully selected (either directly or all their stores)
    const fullySelectedCities = brand.cities.filter((c: any) => {
      return selection.has(`city:${c.id}`) || c.stores.every((s: any) => selection.has(`store:${s.id}`))
    })

    if (fullySelectedCities.length === 0) {
      // No cities fully selected - remove brand
      selection.delete(`brand:${brand.id}`)
    } else if (fullySelectedCities.length === brand.cities.length && brand.cities.length > 0) {
      // All cities fully selected - add brand for consolidation (but keep children for accurate counting)
      selection.add(`brand:${brand.id}`)
    } else {
      // Some cities selected - remove brand (will show as indeterminate)
      selection.delete(`brand:${brand.id}`)
    }
  }

  const isItemSelected = (itemId: string, type: 'brand' | 'city' | 'store'): boolean => {
    // For stores: only check direct selection
    if (type === 'store') {
      return localSelectedItems.has(`store:${itemId}`)
    }

    // For cities: check direct selection OR if all stores are selected
    if (type === 'city') {
      // Direct selection
      if (localSelectedItems.has(`city:${itemId}`)) return true

      // Check if all stores in this city are selected
      const city = hierarchicalData.flatMap(b => b.cities).find(c => c.id === itemId)
      if (!city || city.stores.length === 0) return false

      return city.stores.every(store => localSelectedItems.has(`store:${store.id}`))
    }

    // For brands: check direct selection OR if all cities are fully selected
    if (type === 'brand') {
      // Direct selection
      if (localSelectedItems.has(`brand:${itemId}`)) return true

      // Check if all cities in this brand are fully selected
      const brand = hierarchicalData.find(b => b.id === itemId)
      if (!brand || brand.cities.length === 0) return false

      return brand.cities.every(city => {
        // City is selected if it's directly selected OR all its stores are selected
        return (
          localSelectedItems.has(`city:${city.id}`) ||
          city.stores.every(store => localSelectedItems.has(`store:${store.id}`))
        )
      })
    }

    return false
  }

  const isItemIndeterminate = (itemId: string, type: 'brand' | 'city') => {
    // If item is directly selected, it's not indeterminate
    if (localSelectedItems.has(`${type}:${itemId}`)) return false

    if (type === 'brand') {
      const brand = hierarchicalData.find(b => b.id === itemId)
      if (!brand) return false

      // Check if some but not all cities are selected/have selected stores
      let hasSelectedChildren = false
      let allChildrenSelected = true

      for (const city of brand.cities) {
        const citySelected =
          localSelectedItems.has(`city:${city.id}`) ||
          city.stores.every(store => localSelectedItems.has(`store:${store.id}`))
        const cityHasSelection =
          localSelectedItems.has(`city:${city.id}`) ||
          city.stores.some(store => localSelectedItems.has(`store:${store.id}`))

        if (cityHasSelection) hasSelectedChildren = true
        if (!citySelected) allChildrenSelected = false
      }

      // Indeterminate if some but not all children are selected
      return hasSelectedChildren && !allChildrenSelected
    } else if (type === 'city') {
      const city = hierarchicalData.flatMap(b => b.cities).find(c => c.id === itemId)
      if (!city) return false

      // Check if some but not all stores are selected
      const selectedStores = city.stores.filter(store => localSelectedItems.has(`store:${store.id}`))
      return selectedStores.length > 0 && selectedStores.length < city.stores.length
    }

    return false
  }

  // Count actual selected items (stores + individual cities + individual brands)
  const getSelectedCount = () => {
    let count = 0

    hierarchicalData.forEach(brand => {
      brand.cities.forEach(city => {
        city.stores.forEach(store => {
          if (isItemSelected(store.id, 'store')) {
            count++
          }
        })
      })
    })

    return count
  }

  return {
    searchTerm,
    setSearchTerm,
    expandedBrands,
    expandedCities,
    localSelectedItems,
    setLocalSelectedItems,
    hierarchicalData,
    filteredData,
    toggleBrandExpansion,
    toggleCityExpansion,
    handleItemToggle,
    isItemSelected,
    isItemIndeterminate,
    getSelectedCount
  }
}
