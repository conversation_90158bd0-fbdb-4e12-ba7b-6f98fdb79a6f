import { useState, useMemo } from 'react'

import { useHierarchicalData } from './use-hierarchical-data'

export function useBrandCityStoreSelector(selectedItems: string[]) {
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedBrands, setExpandedBrands] = useState<Set<string>>(new Set())
  const [expandedCities, setExpandedCities] = useState<Set<string>>(new Set())

  // Chỉ lưu stores được chọn làm nguồn sự thật chính
  const [selectedStores, setSelectedStores] = useState<Set<string>>(() => {
    const storeIds = new Set<string>()
    selectedItems.forEach(item => {
      if (item.startsWith('store:')) {
        storeIds.add(item.replace('store:', ''))
      }
    })
    return storeIds
  })

  const { hierarchicalData, filterHierarchicalData } = useHierarchicalData()

  const filteredData = useMemo(() => {
    return filterHierarchicalData(searchTerm)
  }, [filterHierarchicalData, searchTerm])

  const toggleBrandExpansion = (brandId: string) => {
    const newExpanded = new Set(expandedBrands)
    if (newExpanded.has(brandId)) {
      newExpanded.delete(brandId)
    } else {
      newExpanded.add(brandId)
    }
    setExpandedBrands(newExpanded)
  }

  const toggleCityExpansion = (cityId: string) => {
    const newExpanded = new Set(expandedCities)
    if (newExpanded.has(cityId)) {
      newExpanded.delete(cityId)
    } else {
      newExpanded.add(cityId)
    }
    setExpandedCities(newExpanded)
  }

  // Helper: Kiểm tra xem city có được chọn hoàn toàn không (tất cả stores được chọn)
  const isCityFullySelected = (cityId: string): boolean => {
    const city = hierarchicalData.flatMap(b => b.cities).find(c => c.id === cityId)
    if (!city || city.stores.length === 0) return false
    return city.stores.every(store => selectedStores.has(store.id))
  }

  // Helper: Kiểm tra xem city có được chọn một phần không (một số stores được chọn)
  const isCityPartiallySelected = (cityId: string): boolean => {
    const city = hierarchicalData.flatMap(b => b.cities).find(c => c.id === cityId)
    if (!city || city.stores.length === 0) return false
    const selectedCount = city.stores.filter(store => selectedStores.has(store.id)).length
    return selectedCount > 0 && selectedCount < city.stores.length
  }

  // Helper: Kiểm tra xem brand có được chọn hoàn toàn không (tất cả cities được chọn hoàn toàn)
  const isBrandFullySelected = (brandId: string): boolean => {
    const brand = hierarchicalData.find(b => b.id === brandId)
    if (!brand || brand.cities.length === 0) return false
    return brand.cities.every(city => isCityFullySelected(city.id))
  }

  // Helper: Kiểm tra xem brand có được chọn một phần không
  const isBrandPartiallySelected = (brandId: string): boolean => {
    const brand = hierarchicalData.find(b => b.id === brandId)
    if (!brand || brand.cities.length === 0) return false

    // Nếu brand đã được chọn hoàn toàn thì không phải indeterminate
    if (isBrandFullySelected(brandId)) return false

    // Indeterminate nếu có ít nhất một city có selection
    return brand.cities.some(city => isCityFullySelected(city.id) || isCityPartiallySelected(city.id))
  }

  const handleItemToggle = (itemId: string, type: 'brand' | 'city' | 'store') => {
    console.log('🔄 Toggle:', { itemId, type })
    const newSelectedStores = new Set(selectedStores)

    if (type === 'brand') {
      const brand = hierarchicalData.find(b => b.id === itemId)
      if (!brand) return

      const isCurrentlySelected = isBrandFullySelected(itemId)

      if (isCurrentlySelected) {
        // Unselect brand - remove all stores in this brand
        console.log('❌ Removing brand and all stores')
        brand.cities.forEach(city => {
          city.stores.forEach(store => {
            newSelectedStores.delete(store.id)
          })
        })
      } else {
        // Select brand - add all stores in this brand
        console.log('✅ Adding brand - selecting all stores')
        brand.cities.forEach(city => {
          city.stores.forEach(store => {
            newSelectedStores.add(store.id)
          })
        })
      }
    } else if (type === 'city') {
      const city = hierarchicalData.flatMap(b => b.cities).find(c => c.id === itemId)
      if (!city) return

      const isCurrentlySelected = isCityFullySelected(itemId)

      if (isCurrentlySelected) {
        // Unselect city - remove all stores in this city
        console.log('❌ Removing city and all stores')
        city.stores.forEach(store => {
          newSelectedStores.delete(store.id)
        })
      } else {
        // Select city - add all stores in this city
        console.log('✅ Adding city - selecting all stores')
        city.stores.forEach(store => {
          newSelectedStores.add(store.id)
        })
      }
    } else if (type === 'store') {
      const isCurrentlySelected = newSelectedStores.has(itemId)

      if (isCurrentlySelected) {
        console.log('❌ Removing store')
        newSelectedStores.delete(itemId)
      } else {
        console.log('✅ Adding store')
        newSelectedStores.add(itemId)
      }
    }

    console.log('🔚 Final selection:', Array.from(newSelectedStores))
    setSelectedStores(newSelectedStores)
  }

  // Interface functions cho UI components
  const isItemSelected = (itemId: string, type: 'brand' | 'city' | 'store'): boolean => {
    if (type === 'store') {
      return selectedStores.has(itemId)
    } else if (type === 'city') {
      return isCityFullySelected(itemId)
    } else if (type === 'brand') {
      return isBrandFullySelected(itemId)
    }
    return false
  }

  const isItemIndeterminate = (itemId: string, type: 'brand' | 'city'): boolean => {
    if (type === 'city') {
      return isCityPartiallySelected(itemId)
    } else if (type === 'brand') {
      return isBrandPartiallySelected(itemId)
    }
    return false
  }

  // Đếm số stores được chọn
  const getSelectedCount = () => {
    return selectedStores.size
  }

  // Tạo localSelectedItems để tương thích với component cha
  const localSelectedItems = new Set<string>()
  selectedStores.forEach(storeId => {
    localSelectedItems.add(`store:${storeId}`)
  })

  return {
    searchTerm,
    setSearchTerm,
    expandedBrands,
    expandedCities,
    localSelectedItems,
    setLocalSelectedItems: (newSelection: Set<string>) => {
      // Convert back to store IDs only
      const storeIds = new Set<string>()
      newSelection.forEach(item => {
        if (item.startsWith('store:')) {
          storeIds.add(item.replace('store:', ''))
        }
      })
      setSelectedStores(storeIds)
    },
    hierarchicalData,
    filteredData,
    toggleBrandExpansion,
    toggleCityExpansion,
    handleItemToggle,
    isItemSelected,
    isItemIndeterminate,
    getSelectedCount
  }
}
