import { useState, useMemo, useEffect } from 'react'

import { useHierarchicalData } from './use-hierarchical-data'

export function useBrandCityStoreSelector(selectedItems: string[]) {
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedBrands, setExpandedBrands] = useState<Set<string>>(new Set())
  const [expandedCities, setExpandedCities] = useState<Set<string>>(new Set())

  const { hierarchicalData, filterHierarchicalData } = useHierarchicalData()

  // Chỉ lưu stores được chọn làm nguồn sự thật chính
  const [selectedStores, setSelectedStores] = useState<Set<string>>(() => {
    const storeIds = new Set<string>()
    selectedItems.forEach(item => {
      if (item.startsWith('store:')) {
        storeIds.add(item.replace('store:', ''))
      }
    })
    return storeIds
  })

  // Sync selectedStores khi selectedItems prop thay đổi (từ GET API)
  useEffect(() => {
    const storeIds = new Set<string>()
    selectedItems.forEach(item => {
      if (item.startsWith('store:')) {
        storeIds.add(item.replace('store:', ''))
      }
    })
    setSelectedStores(storeIds)
    console.log('🔄 Syncing selectedStores from props:', {
      selectedItems,
      storeIds: Array.from(storeIds),
      hierarchicalDataLength: hierarchicalData.length
    })
  }, [selectedItems, hierarchicalData])

  const filteredData = useMemo(() => {
    return filterHierarchicalData(searchTerm)
  }, [filterHierarchicalData, searchTerm])

  const toggleBrandExpansion = (brandId: string) => {
    const newExpanded = new Set(expandedBrands)
    if (newExpanded.has(brandId)) {
      newExpanded.delete(brandId)
    } else {
      newExpanded.add(brandId)
    }
    setExpandedBrands(newExpanded)
  }

  const toggleCityExpansion = (cityId: string) => {
    const newExpanded = new Set(expandedCities)
    if (newExpanded.has(cityId)) {
      newExpanded.delete(cityId)
    } else {
      newExpanded.add(cityId)
    }
    setExpandedCities(newExpanded)
  }

  const handleItemToggle = (itemId: string, type: 'brand' | 'city' | 'store') => {
    console.log('🔄 Toggle:', { itemId, type })
    const newSelectedStores = new Set(selectedStores)

    if (type === 'brand') {
      const brand = hierarchicalData.find(b => b.id === itemId)
      if (!brand) return

      const isCurrentlySelected = isBrandHasAnySelection(itemId)

      if (isCurrentlySelected) {
        // Unselect brand - remove all stores in this brand
        console.log('❌ Removing brand and all stores')
        brand.cities.forEach(city => {
          city.stores.forEach(store => {
            newSelectedStores.delete(store.id)
          })
        })
      } else {
        // Select brand - add all stores in this brand
        console.log('✅ Adding brand - selecting all stores')
        brand.cities.forEach(city => {
          city.stores.forEach(store => {
            newSelectedStores.add(store.id)
          })
        })
      }
    } else if (type === 'city') {
      const city = hierarchicalData.flatMap(b => b.cities).find(c => c.id === itemId)
      if (!city) return

      const isCurrentlySelected = isCityHasAnySelection(itemId)

      if (isCurrentlySelected) {
        // Unselect city - remove all stores in this city
        console.log('❌ Removing city and all stores')
        city.stores.forEach(store => {
          newSelectedStores.delete(store.id)
        })
      } else {
        // Select city - add all stores in this city
        console.log('✅ Adding city - selecting all stores')
        city.stores.forEach(store => {
          newSelectedStores.add(store.id)
        })
      }
    } else if (type === 'store') {
      const isCurrentlySelected = newSelectedStores.has(itemId)

      if (isCurrentlySelected) {
        console.log('❌ Removing store')
        newSelectedStores.delete(itemId)

        // Cascading logic: Kiểm tra và cập nhật parent states
        // Tìm city và brand chứa store này
        const city = hierarchicalData.flatMap(b => b.cities).find(c => c.stores.some(s => s.id === itemId))
        const brand = hierarchicalData.find(b => b.cities.some(c => c.stores.some(s => s.id === itemId)))

        if (city && brand) {
          // Kiểm tra xem city còn store nào được chọn không
          const remainingStoresInCity = city.stores.filter(s => s.id !== itemId && newSelectedStores.has(s.id))
          if (remainingStoresInCity.length === 0) {
            console.log('❌ City không còn stores nào được chọn')
            // Kiểm tra brand còn city nào có stores được chọn không
            const remainingCitiesWithStores = brand.cities.filter(
              c => c.id !== city.id && c.stores.some(s => newSelectedStores.has(s.id))
            )
            if (remainingCitiesWithStores.length === 0) {
              console.log('❌ Brand không còn cities nào có stores được chọn')
            }
          }
        }
      } else {
        console.log('✅ Adding store')
        newSelectedStores.add(itemId)

        // Cascading logic: Tự động chọn parents nếu chưa có gì được chọn
        const city = hierarchicalData.flatMap(b => b.cities).find(c => c.stores.some(s => s.id === itemId))
        const brand = hierarchicalData.find(b => b.cities.some(c => c.stores.some(s => s.id === itemId)))

        if (city && brand) {
          // Kiểm tra xem có store nào khác trong brand đã được chọn chưa
          const hasOtherSelectedStores = brand.cities.some(c =>
            c.stores.some(s => s.id !== itemId && newSelectedStores.has(s.id))
          )

          if (!hasOtherSelectedStores) {
            console.log('✅ Đây là store đầu tiên trong brand - cascading selection')
            // Đây là store đầu tiên được chọn trong brand
            // Logic cascading sẽ được xử lý bởi UI state tự động
          }
        }
      }
    }

    console.log('🔚 Final selection:', Array.from(newSelectedStores))
    setSelectedStores(newSelectedStores)
  }

  // Helper: Kiểm tra xem city có ít nhất 1 store được chọn không
  const isCityHasAnySelection = (cityId: string): boolean => {
    const city = hierarchicalData.flatMap(b => b.cities).find(c => c.id === cityId)
    if (!city || city.stores.length === 0) return false
    return city.stores.some(store => selectedStores.has(store.id))
  }

  // Helper: Kiểm tra xem brand có ít nhất 1 store được chọn không
  const isBrandHasAnySelection = (brandId: string): boolean => {
    const brand = hierarchicalData.find(b => b.id === brandId)
    if (!brand || brand.cities.length === 0) return false
    return brand.cities.some(city => city.stores.some(store => selectedStores.has(store.id)))
  }

  // Interface functions cho UI components
  const isItemSelected = (itemId: string, type: 'brand' | 'city' | 'store'): boolean => {
    if (type === 'store') {
      return selectedStores.has(itemId)
    } else if (type === 'city') {
      // City được tick khi có ít nhất 1 store được chọn
      return isCityHasAnySelection(itemId)
    } else if (type === 'brand') {
      // Brand được tick khi có ít nhất 1 store được chọn
      return isBrandHasAnySelection(itemId)
    }
    return false
  }

  const isItemIndeterminate = (_itemId: string, _type: 'brand' | 'city'): boolean => {
    // Với logic mới, không có indeterminate state
    // Vì parent luôn được tick khi có ít nhất 1 child được chọn
    return false
  }

  // Đếm số stores được chọn
  const getSelectedCount = () => {
    return selectedStores.size
  }

  // Tạo localSelectedItems để tương thích với component cha
  const localSelectedItems = new Set<string>()
  selectedStores.forEach(storeId => {
    localSelectedItems.add(`store:${storeId}`)
  })

  return {
    searchTerm,
    setSearchTerm,
    expandedBrands,
    expandedCities,
    localSelectedItems,
    setLocalSelectedItems: (newSelection: Set<string>) => {
      // Convert back to store IDs only
      const storeIds = new Set<string>()
      newSelection.forEach(item => {
        if (item.startsWith('store:')) {
          storeIds.add(item.replace('store:', ''))
        }
      })
      setSelectedStores(storeIds)
    },
    hierarchicalData,
    filteredData,
    toggleBrandExpansion,
    toggleCityExpansion,
    handleItemToggle,
    isItemSelected,
    isItemIndeterminate,
    getSelectedCount
  }
}
