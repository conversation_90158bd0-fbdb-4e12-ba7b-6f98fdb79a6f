import { useState, useEffect } from 'react'

import type { UseFormReturn } from 'react-hook-form'

import { HelpCircle, ChevronDown, ChevronRight, Check, ChevronsUpDown, X } from 'lucide-react'

import { useAuthStore } from '@/stores/authStore'

import { cn } from '@/lib/utils'

import { useCustomizationsData, useCustomizationById, useItemsData, useSourcesData } from '@/hooks/api'
import { usePosCitiesData } from '@/hooks/local-storage'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

import type { ItemsInCity } from '../data'
import { useItemsInCityData, itemsInCityApiService } from '../hooks'
import { BuffetConfigModal } from './buffet-config-modal'
import type { FormValues } from './items-in-city-mutate'
import { PriceSourceDialog } from './price-source-dialog'

interface Props {
  form: UseFormReturn<FormValues>
  currentRow?: ItemsInCity
}

export function ItemConfiguration({ form, currentRow }: Props) {
  const [showQuantityInputs, setShowQuantityInputs] = useState(false)
  const [isCustomizationDetailsOpen, setIsCustomizationDetailsOpen] = useState(false)
  const [isPriceSourceDialogOpen, setIsPriceSourceDialogOpen] = useState(false)
  const [isBuffetConfigModalOpen, setIsBuffetConfigModalOpen] = useState(false)
  const [confirmDeleteIndex, setConfirmDeleteIndex] = useState<number | null>(null)

  const selectedCityUid = form.watch('city_uid')
  const selectedCustomizationUid = form.watch('customization_uid')
  const isVirtualItem = form.watch('is_virtual_item')
  const isBuffetItem = form.watch('is_buffet_item')

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { getCityByName } = usePosCitiesData()

  const { data: sourcesData = [] } = useSourcesData({
    company_uid: company?.id,
    brand_uid: selectedBrand?.id,
    skip_limit: true,
    enabled: !!company?.id && !!selectedBrand?.id
  })

  const getSourceName = (sourceId: string): string => {
    const source = sourcesData.find((s: any) => s.sourceId === sourceId || s.source_id === sourceId)
    return source?.sourceName || sourceId
  }

  const getActualCityUid = (cityUidOrName: string): string | null => {
    if (!cityUidOrName) return null

    const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    if (guidRegex.test(cityUidOrName)) {
      return cityUidOrName
    }

    const cityByName = getCityByName(cityUidOrName)
    return cityByName?.id || null
  }

  const actualCityUid = getActualCityUid(selectedCityUid)

  const { data: itemsData = [] } = useItemsInCityData({
    params: {
      city_uid: actualCityUid || undefined,
      skip_limit: true,
      active: 1
    },
    enabled: isBuffetConfigModalOpen && !!actualCityUid
  })

  const menuItemsForBuffet: ItemsInCity[] = itemsData.map(item => ({
    id: item.id || '',
    code: item.item_id || '',
    name: item.item_name || '',
    price: item.ots_price || 0,
    vatPercent: 0,
    categoryGroup: '',
    itemType: '',
    itemClass: '',
    unit: '',
    sideItems: '',
    city: '',
    buffetConfig: '',
    customization: '',
    isActive: item.active || false,
    createdAt: new Date(typeof item.created_at === 'number' ? item.created_at : Date.now()),
    originalData: {
      item_id: item.item_id || '',
      item_code: item.item_id || '',
      item_name: item.item_name || '',
      price: item.ots_price || 0,
      vat_percent: 0,
      category_group: '',
      item_type: '',
      unit: '',
      side_items: '',
      city_name: '',
      buffet_config: '',
      customization_uid: '',
      active: item.active ? 1 : 0,
      created_at: typeof item.created_at === 'number' ? item.created_at : Date.now(),
      store_uid: '',
      store_name: ''
    }
  }))
  const priceBySource = form.watch('price_by_source') || []
  const excludeItemsBuffet = form.watch('exclude_items_buffet') || []
  const upSizeBuffet = form.watch('up_size_buffet') || []

  const getAllCityUids = (): string[] => {
    try {
      const citiesData = localStorage.getItem('pos_cities_data')
      if (citiesData) {
        const cities: Array<{ id: string }> = JSON.parse(citiesData)
        return cities.map(city => city.id)
      }
    } catch {}
    return []
  }

  const customizationCityUids = selectedCityUid && selectedCityUid !== 'all' ? [selectedCityUid] : getAllCityUids()

  const { data: customizations = [] } = useCustomizationsData({
    skip_limit: true,
    list_city_uid: customizationCityUids.length > 0 ? customizationCityUids : undefined,
    enabled: !!selectedCityUid
  })

  const { data: customizationDetails } = useCustomizationById(
    selectedCustomizationUid || '',
    !!selectedCustomizationUid && selectedCustomizationUid !== 'none'
  )

  const { data: items = [] } = useItemsData({
    params: {
      city_uid: selectedCityUid,
      skip_limit: true
    },
    enabled: !!selectedCityUid && !!selectedCustomizationUid && selectedCustomizationUid !== 'none'
  })

  const handleToggleQuantityPricing = () => {
    if (!showQuantityInputs) {
      const currentCrossPrice = form.getValues('cross_price') || []
      if (currentCrossPrice.length === 0) {
        form.setValue('cross_price', [{ quantity: 2, price: 0 }])
      }
    } else {
      form.setValue('cross_price', [])
    }
    setShowQuantityInputs(!showQuantityInputs)
  }

  const handlePriceSourceConfirm = (data: { source: string; amount: number; sourceName?: string }) => {
    const currentSources = form.getValues('price_by_source') || []
    const newSource = {
      source: data.source,
      name: data.sourceName || data.source,
      amount: data.amount
    }
    form.setValue('price_by_source', [...currentSources, newSource])
    setIsPriceSourceDialogOpen(false)
  }

  const handleRemovePriceSource = (index: number) => {
    setConfirmDeleteIndex(index)
  }

  const confirmRemovePriceSource = () => {
    if (confirmDeleteIndex !== null) {
      const newSources = priceBySource.filter((_: any, i: number) => i !== confirmDeleteIndex)
      form.setValue('price_by_source', newSources)
      setConfirmDeleteIndex(null)
    }
  }

  const menuItem: ItemsInCity = currentRow || {
    id: form.getValues('item_id') || '',
    name: form.getValues('item_name') || 'Món ăn mới',
    code: form.getValues('item_id') || '',
    price: form.getValues('ots_price') || 0,
    vatPercent: 0,
    categoryGroup: '',
    itemType: '',
    itemClass: '',
    unit: '',
    city: '',
    buffetConfig: 'Chưa cấu hình',
    isActive: true,
    createdAt: new Date(),
    originalData: {
      item_id: form.getValues('item_id') || '',
      item_code: form.getValues('item_id') || '',
      item_name: form.getValues('item_name') || 'Món ăn mới',
      price: form.getValues('ots_price') || 0,
      vat_percent: 0,
      category_group: '',
      item_type: '',
      unit: '',
      city_name: '',
      buffet_config: 'Chưa cấu hình',
      active: 1,
      created_at: Date.now() / 1000,
      store_uid: '',
      store_name: ''
    }
  }

  useEffect(() => {
    const fetchItemData = async () => {
      if (currentRow?.id && company?.id && selectedBrand?.id) {
        try {
          const apiResponse = await itemsInCityApiService.getItemById({
            id: currentRow.id,
            company_uid: company.id,
            brand_uid: selectedBrand.id
          })

          const realItemData = apiResponse.data as any
          const extraData = realItemData?.extra_data

          if (extraData) {
            const excludeItems = extraData.exclude_items_buffet || []
            const upSizeItems = extraData.up_size_buffet || []
            const priceSources = extraData.price_by_source || []
            const crossPrices = extraData.cross_price || []
            form.setValue('exclude_items_buffet', excludeItems)
            form.setValue('up_size_buffet', upSizeItems)
            form.setValue('price_by_source', priceSources)
            form.setValue('cross_price', crossPrices)

            if (crossPrices.length > 0) {
              setShowQuantityInputs(true)
            }
          }
        } catch (error) {
          if (currentRow?.originalData) {
            const extraData = (currentRow.originalData as any)?.extra_data
            if (extraData) {
              const excludeItems = extraData.exclude_items_buffet || []
              const upSizeItems = extraData.up_size_buffet || []
              const priceSources = extraData.price_by_source || []
              const crossPrices = extraData.cross_price || []
              form.setValue('exclude_items_buffet', excludeItems)
              form.setValue('up_size_buffet', upSizeItems)
              form.setValue('price_by_source', priceSources)
              form.setValue('cross_price', crossPrices)

              if (crossPrices.length > 0) {
                setShowQuantityInputs(true)
              }
            }
          }
        }
      } else if (currentRow?.originalData) {
        const extraData = (currentRow.originalData as any)?.extra_data
        if (extraData) {
          const excludeItems = extraData.exclude_items_buffet || []
          const upSizeItems = extraData.up_size_buffet || []
          const priceSources = extraData.price_by_source || []
          const crossPrices = extraData.cross_price || []
          form.setValue('exclude_items_buffet', excludeItems)
          form.setValue('up_size_buffet', upSizeItems)
          form.setValue('price_by_source', priceSources)
          form.setValue('cross_price', crossPrices)

          if (crossPrices.length > 0) {
            setShowQuantityInputs(true)
          }
        }
      }
    }

    fetchItemData()
  }, [currentRow, form, company?.id, selectedBrand?.id])

  return (
    <div className='space-y-6'>
      {/* Cấu hình section */}
      <div className='space-y-4'>
        <h3 className='border-b pb-2 text-lg font-medium'>Cấu hình sửa giá, nhập số lượng</h3>
        <div className='space-y-3'>
          <FormField
            control={form.control}
            name='enable_edit_price'
            render={({ field }) => (
              <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                <FormControl>
                  <Checkbox checked={!!field.value} onCheckedChange={checked => field.onChange(checked ? 1 : 0)} />
                </FormControl>
                <div className='space-y-1 leading-none'>
                  <FormLabel>Cho phép sửa giá khi bán</FormLabel>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='is_print_label'
            render={({ field }) => (
              <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <div className='space-y-1 leading-none'>
                  <FormLabel>Yêu cầu nhập số lượng khi gọi đồ</FormLabel>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name='is_allow_discount'
            render={({ field }) => (
              <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <div className='space-y-1 leading-none'>
                  <FormLabel>Cho phép bỏ món này khỏng cần quyền áp dụng</FormLabel>
                </div>
              </FormItem>
            )}
          />
        </div>

        <div className='space-y-3'>
          <FormField
            control={form.control}
            name='is_virtual_item'
            render={({ field }) => (
              <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                <FormControl>
                  <Checkbox checked={!!field.value} onCheckedChange={checked => field.onChange(checked ? 1 : 0)} />
                </FormControl>
                <div className='space-y-1 leading-none'>
                  <FormLabel>Cấu hình món ảo</FormLabel>
                </div>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='is_buffet_item'
            render={({ field }) => (
              <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                <FormControl>
                  <Checkbox checked={!!field.value} onCheckedChange={checked => field.onChange(checked ? 1 : 0)} />
                </FormControl>
                <div className='space-y-1 leading-none'>
                  <FormLabel>Cấu hình món ăn là vé buffet</FormLabel>
                </div>
              </FormItem>
            )}
          />
          {/* InQR Formula */}
          <FormField
            control={form.control}
            name='formula_qrcode'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Công thức inQr cho máy pha trà</FormLabel>
                <FormControl>
                  <Input placeholder='Nhập công thức InQR' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Buffet Configuration - chỉ hiện khi check vào is_buffet_item */}
          {isBuffetItem === 1 && (
            <div className='space-y-4'>
              <div className='grid grid-cols-[200px_1fr] items-center gap-4'>
                <span className='text-sm font-medium text-gray-600'>Danh sách món không đi kèm vé buffet</span>
                <Button
                  type='button'
                  variant='outline'
                  className='justify-start text-blue-600'
                  onClick={() => setIsBuffetConfigModalOpen(true)}
                >
                  {excludeItemsBuffet.length} món
                </Button>
              </div>

              <div className='grid grid-cols-[200px_1fr] items-center gap-4'>
                <span className='text-sm font-medium text-gray-600'>Danh sách vé buffet được upsize</span>
                <Button
                  type='button'
                  variant='outline'
                  className='justify-start text-blue-600'
                  onClick={() => setIsBuffetConfigModalOpen(true)}
                >
                  {upSizeBuffet.length} món
                </Button>
              </div>
            </div>
          )}
        </div>
        <h3 className='border-b pb-2 text-lg font-medium'>Cấu hình món dịch vụ</h3>
        <FormField
          control={form.control}
          name='is_service'
          render={({ field }) => (
            <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
              <FormControl>
                <Checkbox
                  checked={Boolean(field.value)}
                  onCheckedChange={checked => field.onChange(Boolean(checked))}
                />
              </FormControl>
              <div className='space-y-1 leading-none'>
                <FormLabel>Cấu hình món dịch vụ</FormLabel>
              </div>
            </FormItem>
          )}
        />

        {/* Conditional section when service is enabled */}
        {form.watch('is_service') && (
          <div className='space-y-4 pt-4'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center space-x-2'>
                <span className='text-sm font-medium'>Cấu hình giá thay đổi theo số lượng</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className='h-4 w-4 cursor-help text-gray-400' />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        Nếu khai báo cấu hình đổi giá theo số lượng thì món sẽ ko tự động áp dụng giá và giảm giá theo
                        thời gian khi chạy của món dịch vụ nữa, chỉ được chọn 1 trong 2
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Button
                type='button'
                size='sm'
                variant={showQuantityInputs ? 'outline' : 'default'}
                className={showQuantityInputs ? '' : 'bg-blue-500 hover:bg-blue-600'}
                onClick={handleToggleQuantityPricing}
              >
                {showQuantityInputs ? 'Xoá' : 'Thêm'}
              </Button>
            </div>

            {showQuantityInputs && (
              <div className='space-y-4'>
                {form.watch('cross_price')?.map((_, index) => (
                  <div key={index} className='grid grid-cols-2 gap-4 rounded-lg border bg-gray-50 p-4'>
                    <FormField
                      control={form.control}
                      name={`cross_price.${index}.quantity`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className='text-sm font-medium text-gray-600'>Khai báo số lượng</FormLabel>
                          <FormControl>
                            <Input
                              type='number'
                              placeholder='1'
                              className='bg-white'
                              {...field}
                              onChange={e => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`cross_price.${index}.price`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className='text-sm font-medium text-gray-600'>
                            Khai báo giá khi vượt qua số lượng trên
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder='0'
                              className='bg-white'
                              value={field.value ? new Intl.NumberFormat('vi-VN').format(field.value) : ''}
                              onChange={e => {
                                const value = e.target.value.replace(/[^\d]/g, '')
                                field.onChange(value ? Number(value) : 0)
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                ))}
                {form.watch('cross_price')?.length === 0 && (
                  <div className='grid grid-cols-2 gap-4 rounded-lg border bg-gray-50 p-4'>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-gray-600'>Khai báo số lượng</label>
                      <Input type='number' placeholder='1' defaultValue='1' className='bg-white' readOnly />
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-gray-600'>
                        Khai báo giá khi vượt qua số lượng trên
                      </label>
                      <Input type='number' placeholder='0' defaultValue='0' className='bg-white' readOnly />
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Cấu hình giá theo nguồn - chỉ hiện khi không phải món ảo */}
        {!isVirtualItem && (
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <FormLabel className='text-sm font-medium text-gray-700'>Cấu hình giá theo nguồn</FormLabel>
              <Button
                type='button'
                size='sm'
                className='bg-blue-500 hover:bg-blue-600'
                onClick={() => setIsPriceSourceDialogOpen(true)}
              >
                Thêm nguồn
              </Button>
            </div>

            {/* Hiển thị danh sách price sources */}
            {priceBySource.length > 0 && (
              <div className='space-y-2'>
                {priceBySource.map((source: any, index: number) => (
                  <div key={index} className='flex items-center justify-between rounded-md border p-3'>
                    <div>
                      <span className='font-medium'>
                        {getSourceName(source.source_id || source.source || source.name)} - Số tiền:{' '}
                        {(source.amount || source.price || 0).toLocaleString()} ₫
                      </span>
                    </div>
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      onClick={() => handleRemovePriceSource(index)}
                      className='text-gray-400 hover:text-gray-600'
                    >
                      <X className='h-4 w-4' />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            <div className='space-y-3'>
              <div className='flex items-center justify-between'>
                <span className='text-sm font-medium'>Cấu hình giá theo khung thời gian</span>
                <Button type='button' size='sm' className='bg-blue-500 hover:bg-blue-600'>
                  Thêm cấu hình
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Customization */}
        {form.watch('city_uid') && (
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <FormLabel className='text-sm font-medium text-gray-700'>Customization</FormLabel>
              <Button type='button' size='sm' className='bg-blue-500 hover:bg-blue-600'>
                Tạo customization
              </Button>
            </div>

            <div className='flex-1'>
              <FormField
                control={form.control}
                name='customization_uid'
                render={({ field }) => (
                  <FormItem>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant='outline'
                            role='combobox'
                            className={cn('w-full justify-between', !field.value && 'text-muted-foreground')}
                          >
                            {field.value && field.value !== 'none'
                              ? customizations.find(customization => customization.id === field.value)?.name || 'None'
                              : field.value === 'none'
                                ? 'Không có'
                                : 'Chọn customization'}
                            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className='w-full p-0'>
                        <Command>
                          <CommandInput placeholder='Tìm kiếm customization...' />
                          <CommandList>
                            <CommandEmpty>Không tìm thấy customization.</CommandEmpty>
                            <CommandGroup>
                              <CommandItem
                                value='none'
                                onSelect={() => {
                                  field.onChange('none')
                                }}
                              >
                                <Check
                                  className={cn('mr-2 h-4 w-4', field.value === 'none' ? 'opacity-100' : 'opacity-0')}
                                />
                                Không có
                              </CommandItem>
                              {customizations.map(customization => (
                                <CommandItem
                                  value={customization.name}
                                  key={customization.id}
                                  onSelect={() => {
                                    field.onChange(customization.id)
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      'mr-2 h-4 w-4',
                                      field.value === customization.id ? 'opacity-100' : 'opacity-0'
                                    )}
                                  />
                                  {customization.name}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Chi tiết Customize */}
            {selectedCustomizationUid && selectedCustomizationUid !== 'none' && customizationDetails && (
              <div className='space-y-4'>
                <Collapsible open={isCustomizationDetailsOpen} onOpenChange={setIsCustomizationDetailsOpen}>
                  <CollapsibleTrigger asChild>
                    <Button
                      type='button'
                      variant='ghost'
                      className='flex w-full items-center justify-between p-2 text-left'
                    >
                      <span className='text-blue-600'>Chi tiết Customize</span>
                      {isCustomizationDetailsOpen ? (
                        <ChevronDown className='h-4 w-4 text-blue-600' />
                      ) : (
                        <ChevronRight className='h-4 w-4 text-blue-600' />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className='space-y-4'>
                    {customizationDetails.data?.LstItem_Options?.map(group => {
                      const groupItems = group.LstItem_Id.map(itemId => {
                        const foundItem = items.find((apiItem: any) => apiItem.item_id === itemId)
                        return {
                          id: foundItem?.id || itemId,
                          name: foundItem?.item_name || itemId,
                          price: foundItem?.ots_price || 0,
                          code: itemId,
                          active: foundItem?.active ?? 1
                        }
                      })

                      return (
                        <div key={group.id} className='space-y-3'>
                          <div>
                            <span className='font-medium'>{group.Name}</span>
                            <span className='ml-2 text-sm text-gray-500'>
                              (Chọn từ {group.Min_Permitted} đến {group.Max_Permitted} món)
                            </span>
                          </div>

                          <div className='grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4'>
                            {groupItems.map(item => {
                              const isActive = (item as any).active === 1
                              return (
                                <div
                                  key={item.id}
                                  className={`rounded-md border p-3 text-center ${
                                    isActive ? 'bg-gray-50' : 'cursor-not-allowed bg-gray-100 opacity-50'
                                  }`}
                                >
                                  <p className={`text-sm font-medium ${isActive ? '' : 'text-gray-400'}`}>
                                    {item.name}
                                  </p>
                                  <p className='mt-1 text-xs text-gray-500'>({item.code})</p>
                                  <p
                                    className={`mt-1 text-sm font-medium ${
                                      isActive ? 'text-green-600' : 'text-gray-400'
                                    }`}
                                  >
                                    {item.price.toLocaleString('vi-VN', {
                                      minimumFractionDigits: 0,
                                      maximumFractionDigits: 0
                                    })}{' '}
                                    ₫
                                  </p>
                                </div>
                              )
                            })}
                          </div>
                        </div>
                      )
                    })}
                  </CollapsibleContent>
                </Collapsible>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Khung thời gian bán */}
      <div className='space-y-4'>
        <FormLabel className='text-sm font-medium text-gray-700'>Khung thời gian bán</FormLabel>

        {/* Chọn ngày */}
        <FormField
          control={form.control}
          name='time_sale_date_week'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='text-sm text-gray-600'>Chọn ngày</FormLabel>
              <div className='grid grid-cols-7 gap-2'>
                {[
                  { name: 'Thứ 2', bit: 4 },
                  { name: 'Thứ 3', bit: 8 },
                  { name: 'Thứ 4', bit: 16 },
                  { name: 'Thứ 5', bit: 32 },
                  { name: 'Thứ 6', bit: 64 },
                  { name: 'Thứ 7', bit: 128 },
                  { name: 'Chủ nhật', bit: 2 }
                ].map(({ name, bit }) => {
                  const bitValue = bit
                  const isSelected = (field.value & bitValue) !== 0
                  return (
                    <Button
                      key={name}
                      type='button'
                      variant={isSelected ? 'default' : 'outline'}
                      size='sm'
                      className={`text-xs ${isSelected ? 'bg-blue-600 text-white' : 'border-blue-600 text-blue-600'}`}
                      onClick={() => {
                        const currentValue = field.value || 0
                        const newValue = isSelected
                          ? currentValue & ~bitValue // Remove bit
                          : currentValue | bitValue // Add bit
                        field.onChange(newValue)
                      }}
                    >
                      {name}
                    </Button>
                  )
                })}
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Chọn giờ */}
        <FormField
          control={form.control}
          name='time_sale_hour_day'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='text-sm text-gray-600'>Chọn giờ</FormLabel>
              <div className='grid grid-cols-10 gap-2'>
                {Array.from({ length: 24 }, (_, i) => ({ hour: i, label: `${i}h` })).map(({ hour, label }) => {
                  const bitValue = 1 << hour
                  const isSelected = (field.value & bitValue) !== 0
                  return (
                    <Button
                      key={hour}
                      type='button'
                      variant={isSelected ? 'default' : 'outline'}
                      size='sm'
                      className={`text-xs ${isSelected ? 'bg-blue-600 text-white' : 'border-blue-600 text-blue-600'}`}
                      onClick={() => {
                        const currentValue = field.value || 0
                        const newValue = isSelected
                          ? currentValue & ~bitValue // Remove bit
                          : currentValue | bitValue // Add bit
                        field.onChange(newValue)
                      }}
                    >
                      {label}
                    </Button>
                  )
                })}
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Thứ tự hiển thị */}
      <div className='grid grid-cols-1 gap-4'>
        <FormField
          control={form.control}
          name='sort'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Thứ tự hiển thị</FormLabel>
              <FormControl>
                <Input
                  type='number'
                  placeholder='Nhập số thứ tự hiển thị'
                  {...field}
                  onChange={e => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Price Source Dialog */}
      <PriceSourceDialog
        open={isPriceSourceDialogOpen}
        onOpenChange={setIsPriceSourceDialogOpen}
        onConfirm={handlePriceSourceConfirm}
        cityUid={selectedCityUid || ''}
      />

      {/* Buffet Config Modal */}
      <BuffetConfigModal
        open={isBuffetConfigModalOpen}
        onOpenChange={setIsBuffetConfigModalOpen}
        menuItem={menuItem}
        menuItems={menuItemsForBuffet}
        isDetailMode={true}
      />

      {/* Confirm Delete Dialog */}
      <Dialog open={confirmDeleteIndex !== null} onOpenChange={() => setConfirmDeleteIndex(null)}>
        <DialogContent className='max-w-sm'>
          <DialogHeader>
            <DialogTitle className='text-center'>Ban có muốn bỏ cấu hình ?</DialogTitle>
          </DialogHeader>
          <DialogFooter className='flex gap-2'>
            <Button variant='outline' onClick={() => setConfirmDeleteIndex(null)}>
              Hủy
            </Button>
            <Button variant='destructive' onClick={confirmRemovePriceSource}>
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
