# Navy Blue Design System

A comprehensive design system built around Navy Blue (#005baa) as the primary brand color, providing consistent visual elements, patterns, and guidelines for the POS application.

## Overview

This design system replaces the previous black-based theme with a sophisticated navy blue color scheme that maintains excellent accessibility while providing a modern, professional appearance.

### Key Features

- 🎨 **Navy Blue Primary Color**: #005baa as the core brand color
- 🌗 **Dark/Light Mode Support**: Seamless theme switching
- ♿ **Accessibility First**: WCAG AA compliant contrast ratios
- 🧩 **Component Library**: Pre-built, consistent UI components
- 📱 **Responsive Design**: Mobile-first approach
- 🎯 **Design Tokens**: Systematic color, typography, and spacing scales

## Color System

### Primary Brand Color

```css
--primary: #005baa; /* Navy Blue 600 */
```

The navy blue primary color provides:
- **Professional appearance** suitable for business applications
- **Excellent contrast** on white backgrounds (7.1:1 ratio)
- **Brand differentiation** from common blue themes
- **Versatility** across different UI contexts

### Color Palettes

#### Navy Blue Scale
```typescript
50:  '#f0f7ff'  // Lightest tint
100: '#e0efff' 
200: '#bae0ff'
300: '#7cc8ff'
400: '#36adff'  // Dark mode primary
500: '#0891ff'
600: '#005baa'  // Primary brand color
700: '#0054a3'
800: '#004286'
900: '#003670'
950: '#002347'  // Darkest shade
```

#### Supporting Colors
- **Slate**: Neutral grays for backgrounds and text
- **Emerald**: Success states and positive actions
- **Red**: Error states and destructive actions  
- **Amber**: Warning states and attention elements

### Semantic Colors

The design system uses semantic color tokens that automatically adapt to light/dark themes:

```typescript
primary: {
  light: '#005baa',
  dark: '#36adff'
}
```

## Typography

### Font Families

- **Primary**: Inter - Clean, readable sans-serif
- **Display**: Manrope - For headlines and branding
- **Monospace**: System monospace - For code elements

### Typography Scale

| Token | Size | Usage |
|-------|------|-------|
| `text-xs` | 12px | Captions, labels |
| `text-sm` | 14px | Body text (small) |
| `text-base` | 16px | Body text (default) |
| `text-lg` | 18px | Body text (large) |
| `text-xl` | 20px | Subheadings |
| `text-2xl` | 24px | Headings |
| `text-3xl` | 30px | Large headings |

## Spacing System

### Base Scale (4px increments)

```typescript
1: '4px'    // 0.25rem
2: '8px'    // 0.5rem  
3: '12px'   // 0.75rem
4: '16px'   // 1rem
6: '24px'   // 1.5rem
8: '32px'   // 2rem
12: '48px'  // 3rem
16: '64px'  // 4rem
```

### Component Spacing Patterns

- **Cards**: 16px padding (md), 24px for large cards
- **Buttons**: 16px horizontal, 8px vertical
- **Forms**: 16px between fields, 8px between label and input
- **Navigation**: 12px horizontal, 8px vertical for items

## Component Guidelines

### Buttons

```tsx
// Primary actions - navy blue background
<Button variant="default">Primary Action</Button>

// Secondary actions - slate background  
<Button variant="secondary">Secondary Action</Button>

// Destructive actions - red background
<Button variant="destructive">Delete</Button>
```

### Cards

```tsx
<Card className="p-6 space-y-4">
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Supporting description</CardDescription>
  </CardHeader>
  <CardContent>
    <!-- Card content -->
  </CardContent>
</Card>
```

### Status Indicators

- **Success**: Emerald (#10b981)
- **Warning**: Amber (#f59e0b)  
- **Error**: Red (#ef4444)
- **Info**: Navy Blue (#005baa)

## Implementation

### CSS Custom Properties

The design system uses CSS custom properties for theming:

```css
:root {
  --primary: #005baa;
  --primary-foreground: #ffffff;
  --background: #ffffff;
  --foreground: #020617;
  /* ... additional properties */
}

.dark {
  --primary: #36adff;
  --primary-foreground: #002347;
  --background: #020617;
  --foreground: #f8fafc;
  /* ... dark theme overrides */
}
```

### Design Tokens Usage

```typescript
import { navyBlue, semanticColors, spacing } from '@/design-system/tokens'

// Use palette colors for specific shades
const brandColor = navyBlue[600] // #005baa

// Use semantic colors for theme-aware components  
const buttonBackground = semanticColors.primary.light

// Use spacing tokens for consistent layout
const cardPadding = spacing[6] // 24px
```

### Tailwind Classes

```tsx
// Navy blue variations
<div className="bg-navy-blue-600 text-white">
  Primary Brand Color
</div>

// Semantic color classes
<Button className="bg-primary text-primary-foreground">
  Theme-aware Button
</Button>

// Custom navy shadows
<Card className="shadow-navy-lg">
  Card with navy blue shadow
</Card>
```

## Accessibility

### Contrast Ratios

All color combinations meet WCAG AA standards:

- **Navy Blue on White**: 7.1:1 (AAA)
- **Dark Navy on Light Backgrounds**: 4.5:1+ (AA)
- **Light Navy on Dark Backgrounds**: 4.5:1+ (AA)

### Focus Indicators

- **Ring Color**: Navy blue (`--ring: #005baa`)
- **Ring Width**: 2px offset
- **Visible on all interactive elements**

### Color Blindness

- Status colors use different hues (red, amber, emerald)
- Icons supplement color-only information
- Text labels clarify color-coded elements

## Development Guidelines

### File Organization

```
src/design-system/
├── tokens/
│   ├── colors.ts
│   ├── typography.ts
│   ├── spacing.ts
│   └── index.ts
├── components/
│   ├── color-showcase.tsx
│   └── typography-showcase.tsx
└── README.md
```

### Best Practices

1. **Use Semantic Colors**: Prefer `semanticColors.primary.light` over `navyBlue[600]`
2. **Theme Testing**: Always test components in both light and dark modes
3. **Accessibility**: Verify contrast ratios for custom color combinations
4. **Consistency**: Use design tokens rather than hardcoded values
5. **Documentation**: Update this README when adding new tokens or patterns

### Adding New Colors

1. Add to appropriate palette in `tokens/colors.ts`
2. Update semantic color mappings if needed
3. Add to Tailwind safelist in `tailwind.config.js`
4. Test accessibility in both themes
5. Update documentation

## Migration Guide

### From Previous Theme

1. Replace `--primary` references with navy blue values
2. Update component styles using new semantic colors
3. Test all interactive states (hover, focus, active)
4. Verify accessibility compliance
5. Update brand assets and documentation

### CSS Variable Updates

```css
/* Old */
--primary: oklch(0.208 0.042 265.755);

/* New */  
--primary: #005baa;
```

## Browser Support

- **Modern Browsers**: Full support with CSS custom properties
- **Legacy Support**: Fallback colors defined in tokens
- **Dark Mode**: Prefers-color-scheme and class-based switching

## Contributing

1. Follow the established token structure
2. Ensure accessibility compliance
3. Test in both light and dark modes
4. Update documentation for new additions
5. Maintain consistency with existing patterns

---

For questions or contributions, refer to the project's main documentation or submit an issue. 