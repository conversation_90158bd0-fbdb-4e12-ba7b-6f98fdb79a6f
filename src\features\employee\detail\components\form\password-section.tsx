import { Control } from 'react-hook-form'

import { PasswordInput } from '@/components/password-input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui'

import type { CreateEmployeeFormData } from './schema/create-employee-form-schema'

interface PasswordSectionProps {
  control: Control<CreateEmployeeFormData>
  isEditMode: boolean
}

export function PasswordSection({ control, isEditMode }: PasswordSectionProps) {
  return (
    <div className='space-y-2'>
      <h3 className='text-base font-medium'>{isEditMode ? 'Đổi mật khẩu' : 'Tạo mật khẩu'}</h3>
      <p>Vui lòng tạo một mật khẩu dài ít nhất 6 ký tự bao gồm chữ và số. Đổi mật khẩu sẽ làm tài khoản đăng xuất khỏi tất cả thiết bị</p>

      <FormField
        control={control}
        name='password'
        render={({ field }) => (
          <FormItem className='grid grid-cols-4 items-center gap-4'>
            <FormLabel className='text-right text-sm'>Mật khẩu *</FormLabel>
            <div className='col-span-3'>
              <FormControl>
                <PasswordInput placeholder={isEditMode ? 'Để trống nếu không đổi' : 'Nhập mật khẩu'} {...field} />
              </FormControl>
              <FormMessage />
            </div>
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name='confirmPassword'
        render={({ field }) => (
          <FormItem className='grid grid-cols-4 items-center gap-4'>
            <FormLabel className='text-right text-sm'>Xác nhận mật khẩu *</FormLabel>
            <div className='col-span-3'>
              <FormControl>
                <PasswordInput placeholder={isEditMode ? 'Để trống nếu không đổi' : 'Nhập lại mật khẩu'} {...field} />
              </FormControl>
              <FormMessage />
            </div>
          </FormItem>
        )}
      />
    </div>
  )
}
