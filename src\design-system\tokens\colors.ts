/**
 * Design System - Color Tokens
 * Primary Brand Color: Navy Blue #005baa
 */

// Base Navy Blue Color Palette
export const navyBlue = {
  50: '#f0f7ff',
  100: '#e0efff',
  200: '#bae0ff',
  300: '#7cc8ff',
  400: '#36adff',
  500: '#0891ff',
  600: '#005baa', // Primary brand color
  700: '#0054a3',
  800: '#004286',
  900: '#003670',
  950: '#002347',
} as const

// Supporting Color Palettes
export const slate = {
  50: '#f8fafc',
  100: '#f1f5f9',
  200: '#e2e8f0',
  300: '#cbd5e1',
  400: '#94a3b8',
  500: '#64748b',
  600: '#475569',
  700: '#334155',
  800: '#1e293b',
  900: '#0f172a',
  950: '#020617',
} as const

export const neutral = {
  50: '#fafafa',
  100: '#f5f5f5',
  200: '#e5e5e5',
  300: '#d4d4d4',
  400: '#a3a3a3',
  500: '#737373',
  600: '#525252',
  700: '#404040',
  800: '#262626',
  900: '#171717',
  950: '#0a0a0a',
} as const

export const emerald = {
  50: '#ecfdf5',
  100: '#d1fae5',
  200: '#a7f3d0',
  300: '#6ee7b7',
  400: '#34d399',
  500: '#10b981',
  600: '#059669',
  700: '#047857',
  800: '#065f46',
  900: '#064e3b',
  950: '#022c22',
} as const

export const red = {
  50: '#fef2f2',
  100: '#fee2e2',
  200: '#fecaca',
  300: '#fca5a5',
  400: '#f87171',
  500: '#ef4444',
  600: '#dc2626',
  700: '#b91c1c',
  800: '#991b1b',
  900: '#7f1d1d',
  950: '#450a0a',
} as const

export const amber = {
  50: '#fffbeb',
  100: '#fef3c7',
  200: '#fde68a',
  300: '#fcd34d',
  400: '#fbbf24',
  500: '#f59e0b',
  600: '#d97706',
  700: '#b45309',
  800: '#92400e',
  900: '#78350f',
  950: '#451a03',
} as const

// Semantic Color Tokens
export const semanticColors = {
  // Primary brand colors
  primary: {
    light: navyBlue[600],
    dark: navyBlue[400],
  },
  primaryForeground: {
    light: '#ffffff',
    dark: navyBlue[950],
  },

  // Secondary colors
  secondary: {
    light: slate[100],
    dark: slate[800],
  },
  secondaryForeground: {
    light: navyBlue[900],
    dark: slate[50],
  },

  // Muted colors
  muted: {
    light: slate[100],
    dark: slate[800],
  },
  mutedForeground: {
    light: slate[500],
    dark: slate[400],
  },

  // Accent colors
  accent: {
    light: slate[100],
    dark: slate[800],
  },
  accentForeground: {
    light: navyBlue[900],
    dark: slate[50],
  },

  // Background colors
  background: {
    light: '#ffffff',
    dark: navyBlue[950],
  },
  foreground: {
    light: navyBlue[950],
    dark: slate[50],
  },

  // Card colors
  card: {
    light: '#ffffff',
    dark: slate[900],
  },
  cardForeground: {
    light: navyBlue[950],
    dark: slate[50],
  },

  // Popover colors
  popover: {
    light: '#ffffff',
    dark: slate[900],
  },
  popoverForeground: {
    light: navyBlue[950],
    dark: slate[50],
  },

  // Border and input colors
  border: {
    light: slate[200],
    dark: slate[800],
  },
  input: {
    light: slate[200],
    dark: slate[800],
  },
  ring: {
    light: navyBlue[600],
    dark: navyBlue[400],
  },

  // Status colors
  destructive: {
    light: red[500],
    dark: red[400],
  },
  destructiveForeground: {
    light: red[50],
    dark: red[950],
  },
  success: {
    light: emerald[500],
    dark: emerald[400],
  },
  successForeground: {
    light: emerald[50],
    dark: emerald[950],
  },
  warning: {
    light: amber[500],
    dark: amber[400],
  },
  warningForeground: {
    light: amber[50],
    dark: amber[950],
  },

  // Sidebar colors
  sidebar: {
    light: '#ffffff',
    dark: navyBlue[950],
  },
  sidebarForeground: {
    light: navyBlue[950],
    dark: slate[50],
  },
  sidebarPrimary: {
    light: navyBlue[600],
    dark: navyBlue[400],
  },
  sidebarPrimaryForeground: {
    light: '#ffffff',
    dark: navyBlue[950],
  },
  sidebarAccent: {
    light: slate[100],
    dark: slate[800],
  },
  sidebarAccentForeground: {
    light: navyBlue[900],
    dark: slate[50],
  },
  sidebarBorder: {
    light: slate[200],
    dark: slate[800],
  },
  sidebarRing: {
    light: navyBlue[600],
    dark: navyBlue[400],
  },
} as const

// Chart colors with navy blue theme
export const chartColors = {
  chart1: navyBlue[600],
  chart2: emerald[500],
  chart3: amber[500],
  chart4: red[500],
  chart5: slate[500],
} as const

// Export all color palettes
export const colorPalettes = {
  navyBlue,
  slate,
  neutral,
  emerald,
  red,
  amber,
} as const

// Color accessibility helpers
export const getContrastColor = (backgroundColor: string): string => {
  // Simple implementation - in a real app, you'd use a proper contrast calculation
  const darkColors: string[] = [
    navyBlue[600],
    navyBlue[700],
    navyBlue[800],
    navyBlue[900],
    navyBlue[950],
    slate[700],
    slate[800],
    slate[900],
    slate[950],
    neutral[700],
    neutral[800],
    neutral[900],
    neutral[950],
  ]

  return darkColors.includes(backgroundColor) ? '#ffffff' : navyBlue[950]
}

export type ColorToken = keyof typeof semanticColors
export type ColorPalette = keyof typeof colorPalettes
