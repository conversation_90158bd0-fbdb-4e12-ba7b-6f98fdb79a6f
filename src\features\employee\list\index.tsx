import { useAuthStore } from '@/stores/authStore'

import { getErrorMessage } from '@/utils/error-utils'

import { useUsersData } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { createEmployeesColumns, EmployeesDataTable, EmployeesHeader } from './components'
import { useEmployeeFilters, useEmployeeExport, useEmployeeActions } from './hooks'

export default function EmployeeListPage() {
  const { company, stores } = useAuthStore(state => state.auth)

  const {
    searchTerm,
    searchQuery,
    handleSearchChange,
    handleSearchSubmit,
    selectedRole,
    setSelectedRole,
    selectedStatus,
    setSelectedStatus,
    selectedBrand,
    setSelectedBrand,
    selectedCity,
    setSelectedCity,
    selectedStore,
    setSelectedStore
  } = useEmployeeFilters()

  const {
    data: employees,
    isLoading: employeesLoading,
    error: employeesError
  } = useUsersData({
    company_uid: company?.id,
    search: searchTerm || undefined,
    brand_uid: selectedBrand !== 'all' ? selectedBrand : undefined,
    city_uid: selectedCity !== 'all' ? selectedCity : undefined,
    store_uid: selectedStore !== 'all' ? selectedStore : undefined
  })

  const { filteredEmployees } = useEmployeeFilters({ employees })

  const { handleExportEmployees, isExporting } = useEmployeeExport({
    employees: filteredEmployees,
    stores
  })

  const { handleInviteEmployee, handleEmployeeClick, handleToggleEmployeeStatus } = useEmployeeActions()

  const isLoading = employeesLoading
  const error = employeesError

  const employeesColumns = createEmployeesColumns(handleToggleEmployeeStatus, stores)

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='container mx-auto px-4 py-8'>
          <EmployeesHeader
            searchTerm={searchQuery}
            onSearchChange={handleSearchChange}
            onSearchSubmit={handleSearchSubmit}
            selectedRole={selectedRole}
            onRoleChange={setSelectedRole}
            selectedStatus={selectedStatus}
            onStatusChange={setSelectedStatus}
            selectedBrand={selectedBrand}
            onBrandChange={setSelectedBrand}
            selectedCity={selectedCity}
            onCityChange={setSelectedCity}
            selectedStore={selectedStore}
            onStoreChange={setSelectedStore}
            onExportEmployees={handleExportEmployees}
            onInviteEmployee={handleInviteEmployee}
            isExporting={isExporting}
          />

          {error ? (
            <div className='py-8 text-center'>
              <p className='text-red-600'>{getErrorMessage(error)}</p>
            </div>
          ) : isLoading ? (
            <div className='py-8 text-center'>
              <p>Đang tải dữ liệu nhân viên...</p>
            </div>
          ) : (
            <EmployeesDataTable columns={employeesColumns} data={filteredEmployees} onRowClick={handleEmployeeClick} />
          )}
        </div>
      </Main>
    </>
  )
}
