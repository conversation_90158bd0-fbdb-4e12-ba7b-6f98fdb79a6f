import { useState, useEffect } from 'react'

import { Check, ChevronsUpDown } from 'lucide-react'

import { cn } from '@/lib/utils'

import { useCreatePrinter } from '@/hooks/api'
import { usePrinterPositionsData } from '@/hooks/api/use-printer-positions'

import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  Button,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  RadioGroup,
  RadioGroupItem,
  Checkbox
} from '@/components/ui'

interface PrinterModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (printerData: PrinterData) => void
  deviceCode?: string
  printerData?: any // For edit mode
  isEditMode?: boolean
}

interface PrinterData {
  name: string
  connectionType: 'USB' | 'LAN' | 'Sunmi' | 'KDS'
  productId?: string
  vendorId?: string
  ipAddress?: string
  port?: string
  printerType: string
  printerPosition?: string
  paperSize: '350' | '500' | '580'
  sheetNumber: number
  printOrder: boolean
  enableSpecialPrint: boolean
  kdsDeviceType?: string
}

export function PrinterModal({
  open,
  onOpenChange,
  onSave,
  deviceCode,
  printerData,
  isEditMode = false
}: PrinterModalProps) {
  const { mutate: createPrinter } = useCreatePrinter()
  const { data: printerPositions = [] } = usePrinterPositionsData()

  const [formData, setFormData] = useState<PrinterData>({
    name: '',
    connectionType: 'USB',
    productId: '',
    vendorId: '',
    ipAddress: '',
    port: '9100',
    printerType: 'In order',
    printerPosition: 'Chọn vị trí máy in',
    paperSize: '580',
    sheetNumber: 1,
    printOrder: false,
    enableSpecialPrint: false,
    kdsDeviceType: ''
  })

  const [openPrinterType, setOpenPrinterType] = useState(false)
  const [openPrinterPosition, setOpenPrinterPosition] = useState(false)

  const printerTypeOptions = [
    { value: 'In hóa đơn', label: 'In hóa đơn', apiValue: 'PRINT_HS' },
    { value: 'In order', label: 'In order', apiValue: 'PRINT_HS' },
    { value: 'In tem', label: 'In tem', apiValue: 'PRINT_LABLE' }
  ]

  useEffect(() => {
    if (isEditMode && printerData) {
      const connectionType =
        printerData.type_printer === 'Usb'
          ? 'USB'
          : printerData.type_printer === 'Wifi'
            ? 'LAN'
            : printerData.type_printer || 'USB'

      const printerType =
        printerData.type === 'PRINT_HS' ? 'In order' : printerData.type === 'PRINT_LABLE' ? 'In tem' : 'In order'

      setFormData({
        name: printerData.printer_name || '',
        connectionType: connectionType as 'USB' | 'LAN' | 'Sunmi' | 'KDS',
        productId: printerData.product_id?.toString() || '',
        vendorId: printerData.vendor_id?.toString() || '',
        ipAddress: printerData.ip || '',
        port: printerData.port?.toString() || '9100',
        printerType: printerType,
        printerPosition:
          printerPositions.find(pos => pos.printer_position_id === printerData.extra_data?.printer_position)
            ?.printer_position_name || 'Chọn vị trí máy in',
        paperSize: printerData.extra_data?.screen_width?.toString() || '580',
        sheetNumber: printerData.extra_data?.sheet_number || 1,
        printOrder: printerData.print_order || false,
        enableSpecialPrint: printerData.extra_data?.no_print_switch_table === 1,
        kdsDeviceType: ''
      })
    }
  }, [isEditMode, printerData, printerPositions])

  const resetForm = () => {
    setFormData({
      name: '',
      connectionType: 'USB',
      productId: '',
      vendorId: '',
      ipAddress: '',
      port: '9100',
      printerType: 'In order',
      printerPosition: 'Chọn vị trí máy in',
      paperSize: '580',
      sheetNumber: 1,
      printOrder: false,
      enableSpecialPrint: false,
      kdsDeviceType: ''
    })
  }

  const isFormValid = () => {
    if (!formData.name.trim()) return false

    if (formData.connectionType === 'USB') {
      return !!(formData.productId?.trim() && formData.vendorId?.trim())
    }

    return true
  }

  const handleSave = () => {
    if (!isFormValid() || !deviceCode) {
      return
    }

    const printerDeviceId = `PRINTER_DEVICE_ID_${Date.now()}`

    const apiData = {
      type_printer:
        formData.connectionType === 'USB' ? 'Usb' : formData.connectionType === 'LAN' ? 'Lan' : formData.connectionType,
      port: parseInt(formData.port || '9100'),
      extra_data: {
        hash_order: false,
        size_label: 'LABEL4x3',
        w_size_label: '38',
        h_size_label: '30',
        gab_label: '3',
        he_label: '220',
        sheet_number: formData.sheetNumber,
        list_categories: [],
        printer_position: formData.printerPosition === 'Chọn vị trí máy in' ? null : formData.printerPosition || null,
        screen_width: parseInt(formData.paperSize),
        rotate_paper: 0,
        no_print_switch_table: 0,
        device_name: null
      },
      product_id: formData.productId || '2',
      vendor_id: formData.vendorId || '2',
      device_code: deviceCode,
      printer_name: formData.name,
      printer_device_id: printerDeviceId,
      type: 'PRINT_HS',
      print_order: formData.printOrder
    }

    createPrinter(apiData, {
      onSuccess: () => {
        onSave(formData)
        onOpenChange(false)
        resetForm()
      },
      onError: error => {
        console.error('Failed to create printer:', error)
      }
    })
  }

  const handleCancel = () => {
    onOpenChange(false)
    resetForm()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className='text-center text-2xl font-medium'>Chi tiết máy in</DialogTitle>
        </DialogHeader>

        <div className='space-y-3 py-4'>
          {/* Connection Type Selection */}
          <div className='space-y-3'>
            <h3 className='text-lg font-medium'>Chọn kết nối máy in</h3>
            <RadioGroup
              value={formData.connectionType}
              onValueChange={(value: 'USB' | 'LAN' | 'Sunmi' | 'KDS') =>
                setFormData(prev => ({ ...prev, connectionType: value }))
              }
              className='flex space-x-6'
            >
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='USB' id='usb' />
                <Label htmlFor='usb'>USB</Label>
              </div>
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='LAN' id='lan' />
                <Label htmlFor='lan'>LAN</Label>
              </div>
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='Sunmi' id='sunmi' />
                <Label htmlFor='sunmi'>Sunmi</Label>
              </div>
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='KDS' id='kds' />
                <Label htmlFor='kds'>KDS</Label>
              </div>
            </RadioGroup>
          </div>

          {/* USB specific fields */}
          {formData.connectionType === 'USB' && (
            <div className='space-y-3'>
              <div className='grid grid-cols-3 items-center gap-4'>
                <Label className='text-right font-medium text-gray-700'>Product ID *</Label>
                <div className='col-span-2'>
                  <Input
                    value={formData.productId}
                    onChange={e => setFormData(prev => ({ ...prev, productId: e.target.value }))}
                    placeholder='Nhập Product ID'
                  />
                </div>
              </div>
              <div className='grid grid-cols-3 items-center gap-4'>
                <Label className='text-right font-medium text-gray-700'>Vendor ID *</Label>
                <div className='col-span-2'>
                  <Input
                    value={formData.vendorId}
                    onChange={e => setFormData(prev => ({ ...prev, vendorId: e.target.value }))}
                    placeholder='Nhập Vendor ID'
                  />
                </div>
              </div>
            </div>
          )}

          {/* LAN specific fields */}
          {formData.connectionType === 'LAN' && (
            <div className='space-y-4'>
              <div className='grid grid-cols-3 items-center gap-4'>
                <Label className='text-right font-medium text-gray-700'>Địa chỉ IP</Label>
                <div className='col-span-2 flex gap-2'>
                  <Input
                    value={formData.ipAddress}
                    onChange={e => setFormData(prev => ({ ...prev, ipAddress: e.target.value }))}
                    placeholder='Nhập địa chỉ IP'
                    className='flex-1'
                  />
                  <Input
                    value={formData.port}
                    onChange={e => setFormData(prev => ({ ...prev, port: e.target.value }))}
                    placeholder='9100'
                    className='w-20'
                  />
                </div>
              </div>
            </div>
          )}

          {/* KDS specific fields */}
          {formData.connectionType === 'KDS' && (
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Thiết bị KDS</Label>
              <div className='col-span-2'>
                <Select
                  value={formData.kdsDeviceType}
                  onValueChange={value => setFormData(prev => ({ ...prev, kdsDeviceType: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Chọn loại máy in' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='kds-type-1'>KDS Type 1</SelectItem>
                    <SelectItem value='kds-type-2'>KDS Type 2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {/* Kiểu máy in section */}
          <div className='space-y-3'>
            <h3 className='text-lg font-medium'>Kiểu máy in</h3>

            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Loại máy in</Label>
              <div className='col-span-2'>
                <Popover open={openPrinterType} onOpenChange={setOpenPrinterType}>
                  <PopoverTrigger asChild>
                    <Button
                      variant='outline'
                      role='combobox'
                      aria-expanded={openPrinterType}
                      className='w-full justify-between'
                    >
                      {formData.printerType || 'Chọn loại máy in'}
                      <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className='w-full p-0'>
                    <Command>
                      <CommandInput placeholder='Tìm kiếm loại máy in...' />
                      <CommandList>
                        <CommandEmpty>Không tìm thấy loại máy in.</CommandEmpty>
                        <CommandGroup>
                          {printerTypeOptions.map(option => (
                            <CommandItem
                              key={option.value}
                              value={option.value}
                              onSelect={() => {
                                setFormData(prev => ({ ...prev, printerType: option.value }))
                                setOpenPrinterType(false)
                              }}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  formData.printerType === option.value ? 'opacity-100' : 'opacity-0'
                                )}
                              />
                              {option.label}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Vị trí máy in</Label>
              <div className='col-span-2'>
                <Popover open={openPrinterPosition} onOpenChange={setOpenPrinterPosition}>
                  <PopoverTrigger asChild>
                    <Button
                      variant='outline'
                      role='combobox'
                      aria-expanded={openPrinterPosition}
                      className='w-full justify-between'
                    >
                      {formData.printerPosition || 'Chọn vị trí máy in'}
                      <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className='w-full p-0'>
                    <Command>
                      <CommandInput placeholder='Tìm kiếm vị trí máy in...' />
                      <CommandList>
                        <CommandEmpty>Không tìm thấy vị trí máy in.</CommandEmpty>
                        <CommandGroup>
                          {printerPositions.map(position => (
                            <CommandItem
                              key={position.id}
                              value={position.printer_position_name}
                              onSelect={() => {
                                setFormData(prev => ({ ...prev, printerPosition: position.printer_position_name }))
                                setOpenPrinterPosition(false)
                              }}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  formData.printerPosition === position.printer_position_name
                                    ? 'opacity-100'
                                    : 'opacity-0'
                                )}
                              />
                              {position.printer_position_name}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Paper size selection */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Khổ giấy in</Label>
              <div className='col-span-2'>
                <RadioGroup
                  value={formData.paperSize}
                  onValueChange={(value: '350' | '500' | '580') => setFormData(prev => ({ ...prev, paperSize: value }))}
                  className='flex space-x-6'
                >
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='350' id='size-350' />
                    <Label htmlFor='size-350'>350 (K58)</Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='500' id='size-500' />
                    <Label htmlFor='size-500'>500 (K80)</Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='580' id='size-580' />
                    <Label htmlFor='size-580'>580 (K80)</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Số liên</Label>
              <div className='col-span-2'>
                <Input
                  type='number'
                  value={formData.sheetNumber}
                  onChange={e => setFormData(prev => ({ ...prev, sheetNumber: parseInt(e.target.value) || 1 }))}
                  placeholder='1'
                  min='1'
                />
              </div>
            </div>

            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Bấm order</Label>
              <div className='col-span-2'>
                <Checkbox
                  checked={formData.printOrder}
                  onCheckedChange={checked => setFormData(prev => ({ ...prev, printOrder: !!checked }))}
                />
              </div>
            </div>

            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-left font-medium text-gray-700'>Máy in này ko thực hiện in phiếu chuyển bàn</Label>
              <div className='col-span-2'>
                <Checkbox
                  checked={formData.enableSpecialPrint}
                  onCheckedChange={checked => setFormData(prev => ({ ...prev, enableSpecialPrint: !!checked }))}
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleCancel}>
            Hủy
          </Button>
          <Button onClick={handleSave} disabled={!isFormValid()}>
            Lưu
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
