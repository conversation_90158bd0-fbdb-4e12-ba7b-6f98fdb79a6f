import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { QUERY_KEYS } from '@/constants/query-keys'

import { itemsInStoreApiService } from './items-in-store-api'
import type {
  CreateItemInStoreRequest,
  UpdateItemInStoreRequest,
  UpdateItemCustomizationRequest,
  DeleteItemInStoreParams,
  DeleteMultipleItemsInStoreParams,
  DownloadTemplateParams
} from './items-in-store-api'

/**
 * Create item in store mutation
 */
export const useCreateItemInStore = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateItemInStoreRequest) => itemsInStoreApiService.createItemInStore(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_FOR_TABLE] })
      toast.success('Tạo món ăn thành công')
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi tạo món ăn')
    }
  })
}

/**
 * Update item in store mutation
 */
export const useUpdateItemInStore = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UpdateItemInStoreRequest) => itemsInStoreApiService.updateItemInStore(data),
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_FOR_TABLE] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_DETAIL, data.id] })
      toast.success('Cập nhật món ăn thành công')
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi cập nhật món ăn')
    }
  })
}

/**
 * Update item customization mutation
 */
export const useUpdateItemCustomization = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UpdateItemCustomizationRequest) => itemsInStoreApiService.updateItemCustomization(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_FOR_TABLE] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_DETAIL] })
      toast.success('Cập nhật customization thành công!')
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi cập nhật tùy chỉnh món ăn')
    }
  })
}

/**
 * Update item buffet config mutation
 */
export const useUpdateItemBuffetConfig = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { item_uid: string; buffet_config: any }) => itemsInStoreApiService.updateItemBuffetConfig(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_FOR_TABLE] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_DETAIL] })
      toast.success('Cập nhật cấu hình buffet thành công!')
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi cập nhật cấu hình buffet')
    }
  })
}

/**
 * Update item status mutation
 */
export const useUpdateItemInStoreStatus = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { item_uid: string; active: boolean }) => itemsInStoreApiService.updateItemInStoreStatus(data),
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_FOR_TABLE] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_DETAIL, data.id] })
      toast.success('Cập nhật trạng thái món ăn thành công')
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi cập nhật trạng thái món ăn')
    }
  })
}

/**
 * Delete item in store mutation
 */
export const useDeleteItemInStore = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: DeleteItemInStoreParams) => itemsInStoreApiService.deleteItemInStore(params),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_FOR_TABLE] })
      toast.success('Xóa món ăn thành công')
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi xóa món ăn')
    }
  })
}

/**
 * Delete multiple items in store mutation
 */
export const useDeleteMultipleItemsInStore = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (params: DeleteMultipleItemsInStoreParams) => itemsInStoreApiService.deleteMultipleItemsInStore(params),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_FOR_TABLE] })
      toast.success('Xóa món ăn thành công')
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi xóa món ăn')
    }
  })
}

/**
 * Download items template mutation
 */
export const useDownloadItemsTemplate = () => {
  return useMutation({
    mutationFn: (params: DownloadTemplateParams) => itemsInStoreApiService.downloadItemsTemplate(params),
    onSuccess: (blob, variables) => {
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `items-template-${variables.template_type}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success('Tải template thành công')
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi tải template')
    }
  })
}

/**
 * Import items mutation
 */
export const useImportItems = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (file: File) => itemsInStoreApiService.importItems(file),
    onSuccess: result => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST] })
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEMS_IN_STORE_FOR_TABLE] })

      if (result.success) {
        toast.success(result.message || 'Import dữ liệu thành công')
      } else {
        toast.error(result.message || 'Import hoàn thành nhưng có một số lỗi')
      }
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi import dữ liệu')
    }
  })
}
