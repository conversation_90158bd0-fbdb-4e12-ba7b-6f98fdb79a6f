import React, { useState } from 'react'

import { ItemsInStore } from '../data'

interface ItemsInStoreContextType {
  // Modal states
  createOpen: boolean
  setCreateOpen: (open: boolean) => void
  copyOpen: boolean
  setCopyOpen: (open: boolean) => void
  updateOpen: boolean
  setUpdateOpen: (open: boolean) => void
  deleteOpen: boolean
  setDeleteOpen: (open: boolean) => void
  exportOpen: boolean
  setExportOpen: (open: boolean) => void
  importOpen: boolean
  setImportOpen: (open: boolean) => void
  buffetConfigOpen: boolean
  setBuffetConfigOpen: (open: boolean) => void
  priceBySourceConfigOpen: boolean
  setPriceBySourceConfigOpen: (open: boolean) => void

  // Current row state
  currentRow: ItemsInStore | null
  setCurrentRow: React.Dispatch<React.SetStateAction<ItemsInStore | null>>
}

const ItemsInStoreContext = React.createContext<ItemsInStoreContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function ItemsInStoreProvider({ children }: Props) {
  // Modal states
  const [createOpen, setCreateOpen] = useState(false)
  const [copyOpen, setCopyOpen] = useState(false)
  const [updateOpen, setUpdateOpen] = useState(false)
  const [deleteOpen, setDeleteOpen] = useState(false)
  const [exportOpen, setExportOpen] = useState(false)
  const [importOpen, setImportOpen] = useState(false)
  const [buffetConfigOpen, setBuffetConfigOpen] = useState(false)
  const [priceBySourceConfigOpen, setPriceBySourceConfigOpen] = useState(false)

  // Current row state
  const [currentRow, setCurrentRow] = useState<ItemsInStore | null>(null)

  return (
    <ItemsInStoreContext
      value={{
        createOpen,
        setCreateOpen,
        copyOpen,
        setCopyOpen,
        updateOpen,
        setUpdateOpen,
        deleteOpen,
        setDeleteOpen,
        exportOpen,
        setExportOpen,
        importOpen,
        setImportOpen,
        buffetConfigOpen,
        setBuffetConfigOpen,
        priceBySourceConfigOpen,
        setPriceBySourceConfigOpen,
        currentRow,
        setCurrentRow
      }}
    >
      {children}
    </ItemsInStoreContext>
  )
}

export const useItemsInStore = () => {
  const itemsInStoreContext = React.useContext(ItemsInStoreContext)

  if (!itemsInStoreContext) {
    throw new Error('useItemsInStore has to be used within <ItemsInStoreContext>')
  }

  return itemsInStoreContext
}
