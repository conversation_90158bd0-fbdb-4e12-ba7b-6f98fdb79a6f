import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

interface FilterOption {
  value: string
  label: string
}

interface FilterDropdownProps {
  value: string
  onValueChange: (value: string) => void
  options: FilterOption[]
  isLoading?: boolean
  placeholder?: string
  className?: string
  allOptionLabel?: string
  loadingText?: string
  emptyText?: string
  showAllOption?: boolean
}

export function FilterDropdown({
  value,
  onValueChange,
  options,
  isLoading = false,
  placeholder = 'Chọn...',
  className = 'w-48',
  allOptionLabel = 'Tất cả',
  loadingText = 'Đang tải...',
  emptyText = 'Không có dữ liệu',
  showAllOption = true
}: FilterDropdownProps) {
  return (
    <Select value={value} onValueChange={onValueChange}>
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {showAllOption && <SelectItem value='all'>{allOptionLabel}</SelectItem>}

        {isLoading ? (
          <SelectItem value='loading' disabled>
            {loadingText}
          </SelectItem>
        ) : (
          <>
            {options.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}

            {!isLoading && options.length === 0 && (
              <SelectItem value='no-data' disabled>
                {emptyText}
              </SelectItem>
            )}
          </>
        )}
      </SelectContent>
    </Select>
  )
}
